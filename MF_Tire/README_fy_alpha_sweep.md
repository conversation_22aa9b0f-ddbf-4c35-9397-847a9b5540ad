# Fy vs Alpha Sweep Analysis

This directory contains Python scripts and results for analyzing the lateral force (Fy) vs slip angle (alpha) relationship using the Pacejka tire model.

## Files Generated

### Python Scripts
- **`fy_alpha_sweep.py`** - Main sweep script that generates Fy vs Alpha data
- **`fy_alpha_analysis.py`** - Analysis script that processes the sweep data and creates detailed plots

### Data Files
- **`fy_alpha_sweep_Fz2000N_10kmh.csv`** - Raw sweep data in CSV format
- **`fy_alpha_sweep_Fz2000N_10kmh.png`** - Basic plot (Fy vs Alpha)
- **`fy_alpha_detailed_analysis.png`** - Detailed analysis plots with 3 subplots

## Simulation Parameters

- **Vertical Load (Fz)**: 2000 N
- **Speed**: 10 km/h (2.78 m/s)
- **Slip Angle Range**: -10° to +10°
- **Step Size**: 0.01° (2001 data points)
- **Slip Ratio (κ)**: 0 (pure lateral slip)
- **Camber Angle (γ)**: 0°

## Key Results

### Force Characteristics
- **Maximum |Fy|**: 1983.3 N at α = -10.0°
- **Maximum Lateral Friction Coefficient**: 1.000
- **Force at α ≈ 0°**: 2.42e-10 N (essentially zero)
- **Force Asymmetry**: -0.00% (perfectly symmetric)

### Tire Stiffness
- **Cornering Stiffness (±0.1°)**: -22,267 N/rad (-388.6 N/deg)
- This means for every 1° increase in slip angle, the lateral force increases by ~389 N
- The negative sign indicates that positive slip angle generates negative lateral force (standard convention)

### Tire Parameters Used
- **Tire Nominal Load (FNOMIN)**: 3000 N
- **Tire Unloaded Radius**: 0.3 m
- **Tire Model**: Pacejka 5.2 (Magic Formula)

## Plot Descriptions

### Basic Plot (`fy_alpha_sweep_Fz2000N_10kmh.png`)
- **Single Plot**: Fy vs Slip Angle - Shows the characteristic linear-to-nonlinear transition

### Detailed Analysis Plot (`fy_alpha_detailed_analysis.png`)
1. **Top**: Full range Fy vs Alpha (-10° to +10°)
2. **Middle**: Zoomed view around zero (±2° range) - Shows linear region
3. **Bottom**: Normalized force Fy/Fz vs Alpha

## Usage

### To Generate New Sweep Data:
```bash
python fy_alpha_sweep.py
```

### To Analyze Existing Data:
```bash
python fy_alpha_analysis.py
```

## Data Format (CSV)

The CSV file contains the following columns:
- `Slip_Angle_[deg]` - Slip angle in degrees
- `Fy_[N]` - Lateral force in Newtons
- `muY_[-]` - Lateral friction coefficient (dimensionless)
- `Fz_[N]` - Vertical load in Newtons (constant)
- `Speed_[km/h]` - Vehicle speed in km/h (constant)

## Technical Notes

### Tire Model
- Uses the Pacejka Magic Formula 5.2 implementation
- All scaling coefficients set to 1.0 (standard conditions)
- Road friction coefficients (LMUX, LMUY) = 1

### Calculation Method
- Pure lateral slip condition (κ = 0, γ = 0)
- Combined slip effects included in the model
- No limit checking applied to allow full range analysis

### Performance
- 2001 data points calculated in < 2 seconds
- High resolution (0.01° step) for accurate cornering stiffness calculation
- Non-interactive plotting for batch processing

## Interpretation

The results show typical tire lateral behavior:
1. **Linear region** around α = 0° with high cornering stiffness
2. **Gradual saturation** as slip angle increases
3. **Maximum force** occurs at the limit of the analyzed range (±10°)
4. **Perfect symmetry** between positive and negative slip angles

### Cornering Stiffness
The cornering stiffness of -22,267 N/rad is a key parameter for:
- Vehicle handling analysis
- Stability control system design
- Understeer/oversteer prediction
- Tire model validation

### Force Saturation
At ±10° slip angle, the tire is approaching saturation but hasn't reached peak force yet, indicating the tire could generate more lateral force at higher slip angles.

## Comparison with Longitudinal Force

| Parameter | Longitudinal (Fx) | Lateral (Fy) |
|-----------|------------------|--------------|
| Load | 4000 N | 2000 N |
| Max Force | 3999.8 N | 1983.3 N |
| Max μ | 1.000 | 1.000 |
| Stiffness | 50,036 N/unit | 22,267 N/rad |
| Peak occurs at | ±16% slip ratio | >±10° slip angle |

## Dependencies

- numpy
- matplotlib
- scipy
- pandas
- mfpy (local package)

## Author

Generated for MFPy tire analysis - 2025-01-05
