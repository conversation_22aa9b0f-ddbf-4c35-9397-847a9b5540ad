#!/usr/bin/env python3
"""
Fx vs Kappa Sweep Analysis
==========================

This script calculates and plots the longitudinal force (Fx) vs slip ratio (kappa) 
relationship using the Pacejka tire model.

Parameters:
- Fz = 4000 N (vertical load)
- Slip ratio: -100% to +100% with 1% step
- Tire speed: 10 km/h
- Pure longitudinal slip (alpha = 0, gamma = 0)

Author: Generated for MFPy analysis
Date: 2025-01-05
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import mfpy
import os

def main():
    """
    Main function to perform Fx vs Kappa sweep analysis
    """
    
    # ========================================
    # SIMULATION PARAMETERS
    # ========================================
    
    # Load parameters
    Fz = 4500  # Vertical load [N]
    
    # Speed parameters
    speed_kmh = 120  # Speed [km/h]
    speed_ms = speed_kmh / 3.6  # Convert to [m/s]
    
    # Slip ratio parameters
    kappa_min = -1.0  # -100%
    kappa_max = 1.0   # +100%
    kappa_step = 0.01  # 1% step
    
    # Other tire parameters
    alpha = 0.0  # Slip angle [rad] - pure longitudinal
    gamma = 0.0  # Camber angle [rad]
    
    # ========================================
    # LOAD TIRE DATA
    # ========================================
    
    # Path to tire parameter file
    tir_file = os.path.join('samples', 'pacejka_tir52_carsim.tir')
    
    try:
        # Read tire data
        tir_data = mfpy.preprocessing.read_tir(tir_file)
        print(f"Successfully loaded tire data from: {tir_file}")
        print(f"Tire nominal load (FNOMIN): {tir_data.VERTICAL.FNOMIN} N")
        print(f"Tire unloaded radius: {tir_data.DIMENSION.UNLOADED_RADIUS} m")
    except Exception as e:
        print(f"Error loading tire data: {e}")
        return
    
    # ========================================
    # GENERATE SWEEP DATA
    # ========================================
    
    # Create slip ratio array
    kappa_array = np.arange(kappa_min, kappa_max + kappa_step, kappa_step)
    n_points = len(kappa_array)
    
    print(f"Generating sweep with {n_points} points")
    print(f"Slip ratio range: {kappa_min*100:.0f}% to {kappa_max*100:.0f}%")
    print(f"Step size: {kappa_step*100:.1f}%")
    
    # Create input arrays (all same size)
    alpha_array = np.full(n_points, alpha)  # Slip angle [rad]
    gamma_array = np.full(n_points, gamma)  # Camber angle [rad]
    Fz_array = np.full(n_points, Fz)       # Vertical load [N]
    Vx_array = np.full(n_points, speed_ms) # Longitudinal speed [m/s]
    
    # Prepare input for mfpy.solve
    input_values = [alpha_array, kappa_array, gamma_array, Fz_array, Vx_array]
    
    # ========================================
    # CALCULATE TIRE FORCES
    # ========================================
    
    print("Calculating tire forces...")
    
    try:
        # Solve using MFPy
        result = mfpy.solve(input_values, tir_data, check_limits=False)
        print("Calculation completed successfully!")
        
        # Extract results
        kappa_result = result.kappa * 100  # Convert to percentage
        Fx_result = result.FX  # Longitudinal force [N]
        muX_result = result.muX  # Friction coefficient [-]
        
        # Print some statistics
        print(f"\nResults Summary:")
        print(f"Maximum Fx: {np.max(np.abs(Fx_result)):.1f} N")
        print(f"Maximum friction coefficient: {np.max(np.abs(muX_result)):.3f}")
        
    except Exception as e:
        print(f"Error during calculation: {e}")
        return
    
    # ========================================
    # PLOTTING
    # ========================================
    
    print("Creating plots...")
    
    # Create figure with single plot
    fig, ax1 = plt.subplots(1, 1, figsize=(10, 6))

    # Plot: Fx vs Kappa
    ax1.plot(kappa_result, Fx_result, 'b-', linewidth=2, label=f'Fz = {Fz} N')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('Slip Ratio [%]')
    ax1.set_ylabel('Longitudinal Force Fx [N]')
    ax1.set_title(f'Longitudinal Force vs Slip Ratio\n(Fz = {Fz} N, Speed = {speed_kmh} km/h)')
    ax1.legend()
    ax1.set_xlim([kappa_min*100, kappa_max*100])

    # Add zero lines
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # Adjust layout
    plt.tight_layout()
    
    # ========================================
    # SAVE RESULTS
    # ========================================
    
    # Save plot
    plot_filename = f'fx_kappa_sweep_Fz{Fz}N_{speed_kmh}kmh.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Plot saved as: {plot_filename}")
    
    # Save data to CSV
    data_filename = f'fx_kappa_sweep_Fz{Fz}N_{speed_kmh}kmh.csv'
    
    # Create data array
    data_array = np.column_stack([
        kappa_result,  # Slip ratio [%]
        Fx_result,     # Longitudinal force [N]
        muX_result,    # Friction coefficient [-]
        Fz_array,      # Vertical load [N]
        Vx_array * 3.6 # Speed [km/h]
    ])
    
    # Save to CSV with header
    header = 'Slip_Ratio_[%],Fx_[N],muX_[-],Fz_[N],Speed_[km/h]'
    np.savetxt(data_filename, data_array, delimiter=',', header=header, comments='')
    print(f"Data saved as: {data_filename}")
    
    # Don't show plot in non-interactive mode
    # plt.show()

    print("\nAnalysis completed successfully!")

if __name__ == "__main__":
    main()
