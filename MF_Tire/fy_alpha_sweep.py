#!/usr/bin/env python3
"""
Fy vs Alpha Sweep Analysis
==========================

This script calculates and plots the lateral force (Fy) vs slip angle (alpha) 
relationship using the Pacejka tire model.

Parameters:
- Fz = 2000 N (vertical load)
- Slip angle: -10° to +10° with 0.01° step
- Tire speed: 10 km/h
- Pure lateral slip (kappa = 0, gamma = 0)

Author: Generated for MFPy analysis
Date: 2025-01-05
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import mfpy
import os

def main():
    """
    Main function to perform Fy vs Alpha sweep analysis
    """
    
    # ========================================
    # SIMULATION PARAMETERS
    # ========================================
    
    # Load parameters
    Fz = 2000  # Vertical load [N]
    
    # Speed parameters
    speed_kmh = 10  # Speed [km/h]
    speed_ms = speed_kmh / 3.6  # Convert to [m/s]
    
    # Slip angle parameters
    alpha_min_deg = -10.0  # -10 degrees
    alpha_max_deg = 10.0   # +10 degrees
    alpha_step_deg = 0.01  # 0.01 degree step
    
    # Convert to radians
    alpha_min = np.deg2rad(alpha_min_deg)
    alpha_max = np.deg2rad(alpha_max_deg)
    alpha_step = np.deg2rad(alpha_step_deg)
    
    # Other tire parameters
    kappa = 0.0  # Slip ratio [-] - pure lateral
    gamma = 0.0  # Camber angle [rad]
    
    # ========================================
    # LOAD TIRE DATA
    # ========================================
    
    # Path to tire parameter file
    tir_file = os.path.join('samples', 'pacejka_tir52.tir')
    
    try:
        # Read tire data
        tir_data = mfpy.preprocessing.read_tir(tir_file)
        print(f"Successfully loaded tire data from: {tir_file}")
        print(f"Tire nominal load (FNOMIN): {tir_data.VERTICAL.FNOMIN} N")
        print(f"Tire unloaded radius: {tir_data.DIMENSION.UNLOADED_RADIUS} m")
    except Exception as e:
        print(f"Error loading tire data: {e}")
        return
    
    # ========================================
    # GENERATE SWEEP DATA
    # ========================================
    
    # Create slip angle array
    alpha_array = np.arange(alpha_min, alpha_max + alpha_step, alpha_step)
    n_points = len(alpha_array)
    
    print(f"Generating sweep with {n_points} points")
    print(f"Slip angle range: {alpha_min_deg:.2f}° to {alpha_max_deg:.2f}°")
    print(f"Step size: {alpha_step_deg:.2f}°")
    
    # Create input arrays (all same size)
    kappa_array = np.full(n_points, kappa)   # Slip ratio [-]
    gamma_array = np.full(n_points, gamma)   # Camber angle [rad]
    Fz_array = np.full(n_points, Fz)         # Vertical load [N]
    Vx_array = np.full(n_points, speed_ms)   # Longitudinal speed [m/s]
    
    # Prepare input for mfpy.solve
    input_values = [alpha_array, kappa_array, gamma_array, Fz_array, Vx_array]
    
    # ========================================
    # CALCULATE TIRE FORCES
    # ========================================
    
    print("Calculating tire forces...")
    
    try:
        # Solve using MFPy
        result = mfpy.solve(input_values, tir_data, check_limits=False)
        print("Calculation completed successfully!")
        
        # Extract results
        alpha_result = np.rad2deg(result.alpha)  # Convert to degrees
        Fy_result = result.FY  # Lateral force [N]
        muY_result = result.muY  # Lateral friction coefficient [-]
        
        # Print some statistics
        print(f"\nResults Summary:")
        print(f"Maximum |Fy|: {np.max(np.abs(Fy_result)):.1f} N")
        print(f"Maximum lateral friction coefficient: {np.max(np.abs(muY_result)):.3f}")
        
        # Find cornering stiffness (slope at alpha = 0)
        zero_idx = np.argmin(np.abs(alpha_result))
        if zero_idx > 5 and zero_idx < len(alpha_result) - 5:
            # Calculate slope using points around zero
            idx_range = 5
            alpha_local = alpha_result[zero_idx-idx_range:zero_idx+idx_range+1]
            fy_local = Fy_result[zero_idx-idx_range:zero_idx+idx_range+1]
            
            # Linear fit to get cornering stiffness
            cornering_stiffness_deg = np.polyfit(alpha_local, fy_local, 1)[0]
            cornering_stiffness_rad = cornering_stiffness_deg * 180 / np.pi
            
            print(f"Cornering stiffness: {cornering_stiffness_rad:.0f} N/rad ({cornering_stiffness_deg:.1f} N/deg)")
        
    except Exception as e:
        print(f"Error during calculation: {e}")
        return
    
    # ========================================
    # PLOTTING
    # ========================================
    
    print("Creating plots...")
    
    # Create figure with single plot
    fig, ax1 = plt.subplots(1, 1, figsize=(10, 6))
    
    # Plot: Fy vs Alpha
    ax1.plot(alpha_result, Fy_result, 'r-', linewidth=2, label=f'Fz = {Fz} N')
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('Slip Angle [°]')
    ax1.set_ylabel('Lateral Force Fy [N]')
    ax1.set_title(f'Lateral Force vs Slip Angle\n(Fz = {Fz} N, Speed = {speed_kmh} km/h)')
    ax1.legend()
    ax1.set_xlim([alpha_min_deg, alpha_max_deg])
    
    # Add zero lines
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # Adjust layout
    plt.tight_layout()
    
    # ========================================
    # SAVE RESULTS
    # ========================================
    
    # Save plot
    plot_filename = f'fy_alpha_sweep_Fz{Fz}N_{speed_kmh}kmh.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Plot saved as: {plot_filename}")
    
    # Save data to CSV
    data_filename = f'fy_alpha_sweep_Fz{Fz}N_{speed_kmh}kmh.csv'
    
    # Create data array
    data_array = np.column_stack([
        alpha_result,  # Slip angle [°]
        Fy_result,     # Lateral force [N]
        muY_result,    # Lateral friction coefficient [-]
        Fz_array,      # Vertical load [N]
        Vx_array * 3.6 # Speed [km/h]
    ])
    
    # Save to CSV with header
    header = 'Slip_Angle_[deg],Fy_[N],muY_[-],Fz_[N],Speed_[km/h]'
    np.savetxt(data_filename, data_array, delimiter=',', header=header, comments='')
    print(f"Data saved as: {data_filename}")
    
    # Don't show plot in non-interactive mode
    # plt.show()
    
    print("\nAnalysis completed successfully!")

if __name__ == "__main__":
    main()
