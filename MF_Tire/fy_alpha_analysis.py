#!/usr/bin/env python3
"""
Fy vs Alpha Analysis Tool
========================

This script provides analysis tools for the Fy vs Alpha sweep data.
It can load existing data or generate new sweep data.

Author: Generated for MFPy analysis
Date: 2025-01-05
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import mfpy
import os
import pandas as pd

def analyze_fy_alpha_data(csv_file):
    """
    Analyze existing Fy vs Alpha data from CSV file
    """
    try:
        # Load data
        data = pd.read_csv(csv_file)
        
        print(f"Loaded data from: {csv_file}")
        print(f"Number of data points: {len(data)}")
        
        # Extract data
        alpha = data['Slip_Angle_[deg]'].values
        fy = data['Fy_[N]'].values
        mu_y = data['muY_[-]'].values
        fz = data['Fz_[N]'].values[0]  # Should be constant
        speed = data['Speed_[km/h]'].values[0]  # Should be constant
        
        # Analysis
        print(f"\n=== ANALYSIS RESULTS ===")
        print(f"Vertical Load (Fz): {fz:.0f} N")
        print(f"Speed: {speed:.1f} km/h")
        print(f"Slip angle range: {alpha.min():.2f}° to {alpha.max():.2f}°")
        
        # Find key points
        max_fy_idx = np.argmax(np.abs(fy))
        max_fy = fy[max_fy_idx]
        max_fy_alpha = alpha[max_fy_idx]
        
        print(f"\nMaximum |Fy|: {abs(max_fy):.1f} N at α = {max_fy_alpha:.2f}°")
        print(f"Maximum lateral friction coefficient: {mu_y.max():.3f}")
        
        # Find zero crossing (around α = 0)
        zero_idx = np.argmin(np.abs(alpha))
        print(f"At α ≈ 0°: Fy = {fy[zero_idx]:.2e} N")
        
        # Calculate cornering stiffness around zero
        # Find points around ±0.1°
        idx_neg01 = np.argmin(np.abs(alpha + 0.1))
        idx_pos01 = np.argmin(np.abs(alpha - 0.1))
        
        if idx_pos01 != idx_neg01:
            # Cornering stiffness in N/deg
            stiffness_deg = (fy[idx_pos01] - fy[idx_neg01]) / (alpha[idx_pos01] - alpha[idx_neg01])
            # Convert to N/rad
            stiffness_rad = stiffness_deg * 180 / np.pi
            print(f"Cornering stiffness (±0.1°): {stiffness_rad:.0f} N/rad ({stiffness_deg:.1f} N/deg)")
        
        # Asymmetry analysis
        pos_max_fy = np.max(np.abs(fy[alpha > 0]))
        neg_max_fy = np.max(np.abs(fy[alpha < 0]))
        if pos_max_fy > 0 and neg_max_fy > 0:
            asymmetry = (pos_max_fy - neg_max_fy) / (pos_max_fy + neg_max_fy) * 200
            print(f"Force asymmetry: {asymmetry:.2f}%")
        
        return {
            'alpha': alpha,
            'fy': fy,
            'mu_y': mu_y,
            'fz': fz,
            'speed': speed,
            'max_fy': max_fy,
            'max_fy_alpha': max_fy_alpha,
            'stiffness_rad': stiffness_rad if 'stiffness_rad' in locals() else None,
            'stiffness_deg': stiffness_deg if 'stiffness_deg' in locals() else None
        }
        
    except Exception as e:
        print(f"Error analyzing data: {e}")
        return None

def create_detailed_plot(analysis_data, output_file=None):
    """
    Create detailed analysis plots
    """
    if analysis_data is None:
        return
    
    alpha = analysis_data['alpha']
    fy = analysis_data['fy']
    mu_y = analysis_data['mu_y']
    fz = analysis_data['fz']
    speed = analysis_data['speed']
    
    # Create figure with 3 subplots
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))
    
    # Plot 1: Full range Fy vs Alpha
    ax1.plot(alpha, fy, 'r-', linewidth=2)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('Slip Angle [°]')
    ax1.set_ylabel('Lateral Force Fy [N]')
    ax1.set_title(f'Fy vs Slip Angle (Full Range)\nFz = {fz:.0f} N, Speed = {speed:.1f} km/h')
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # Plot 2: Zoomed view around zero
    zoom_range = 2.0  # ±2°
    zoom_mask = (alpha >= -zoom_range) & (alpha <= zoom_range)
    ax2.plot(alpha[zoom_mask], fy[zoom_mask], 'b-', linewidth=2, marker='o', markersize=2)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlabel('Slip Angle [°]')
    ax2.set_ylabel('Lateral Force Fy [N]')
    ax2.set_title(f'Fy vs Slip Angle (±{zoom_range}° Range)')
    ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax2.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # Plot 3: Fy/Fz ratio (normalized force)
    fy_normalized = fy / fz
    ax3.plot(alpha, fy_normalized, 'm-', linewidth=2)
    ax3.grid(True, alpha=0.3)
    ax3.set_xlabel('Slip Angle [°]')
    ax3.set_ylabel('Normalized Force Fy/Fz [-]')
    ax3.set_title('Normalized Lateral Force')
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax3.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Detailed analysis plot saved as: {output_file}")
    
    return fig

def main():
    """
    Main analysis function
    """
    print("=== Fy vs Alpha Analysis Tool ===\n")
    
    # Check if sweep data exists
    csv_file = "fy_alpha_sweep_Fz2000N_10kmh.csv"
    
    if os.path.exists(csv_file):
        print(f"Found existing data file: {csv_file}")
        
        # Analyze existing data
        analysis_data = analyze_fy_alpha_data(csv_file)
        
        if analysis_data:
            # Create detailed plots
            plot_file = "fy_alpha_detailed_analysis.png"
            create_detailed_plot(analysis_data, plot_file)
            
            print(f"\n=== FILES GENERATED ===")
            print(f"Detailed analysis plot: {plot_file}")
    else:
        print(f"Data file {csv_file} not found.")
        print("Please run fy_alpha_sweep.py first to generate the data.")
    
    print("\nAnalysis completed!")

if __name__ == "__main__":
    main()
