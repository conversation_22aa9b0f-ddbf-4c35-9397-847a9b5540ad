import os
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import re
import pandas as pd

def load_data_files(directory_path):
    """
    加载指定目录中的所有txt文件数据
    返回按序号分组的数据字典
    """
    data_groups = defaultdict(list)

    # 获取目录中的所有txt文件
    txt_files = [f for f in os.listdir(directory_path) if f.endswith('.txt')]

    for filename in txt_files:
        # 提取文件名开头的序号
        match = re.match(r'^(\d+)_(.+)\.txt$', filename)
        if match:
            group_num = int(match.group(1))
            variable_name = match.group(2)

            # 读取文件数据
            file_path = os.path.join(directory_path, filename)
            try:
                data = np.loadtxt(file_path)
                data_groups[group_num].append({
                    'name': variable_name,
                    'data': data,
                    'filename': filename
                })
            except Exception as e:
                print(f"Error loading {filename}: {e}")

    return data_groups

def create_time_array(data_length, dt=0.001):
    """
    创建时间数组，假设时间步长为0.001秒
    """
    return np.arange(0, data_length * dt, dt)

def load_csv_data(csv_path):
    """
    读取CSV文件数据并按照指定规则分组
    前7列每列一个组，后续每4列一个组
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_path)
        print(f"CSV文件包含 {len(df.columns)} 列，{len(df)} 行数据")

        csv_groups = {}
        col_names = df.columns.tolist()

        # 前7列，每列一个组
        for i in range(min(7, len(col_names))):
            csv_groups[i + 1] = [{
                'name': col_names[i],
                'data': df.iloc[:, i].values
            }]

        # 后续列，每4列一个组
        remaining_cols = col_names[7:]
        group_num = 8
        for i in range(0, len(remaining_cols), 4):
            group_data = []
            for j in range(4):
                if i + j < len(remaining_cols):
                    col_idx = 7 + i + j
                    group_data.append({
                        'name': remaining_cols[i + j],
                        'data': df.iloc[:, col_idx].values
                    })
            if group_data:
                csv_groups[group_num] = group_data
                group_num += 1

        return csv_groups

    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return {}

def plot_comparison_data(data_groups, csv_groups=None, save_path=None):
    """
    绘制对比数据图表
    """
    # 计算子图布局
    num_groups = len(data_groups)
    cols = 4  # 每行4个子图
    rows = 3  # 固定3行

    # 创建图形
    fig, axes = plt.subplots(rows, cols, figsize=(20, 15))
    # 确保axes是二维数组
    if rows == 1:
        axes = axes.reshape(1, -1)
    elif cols == 1:
        axes = axes.reshape(-1, 1)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 按序号排序绘制
    sorted_groups = sorted(data_groups.items())

    for idx, (group_num, group_data) in enumerate(sorted_groups):
        row = idx // cols
        col = idx % cols
        ax = axes[row, col]

        # 为每个变量绘制CarSim曲线（蓝色）
        for var_data in group_data:
            data = var_data['data']
            name = var_data['name']

            # 创建时间数组
            time = create_time_array(len(data))

            # 绘制CarSim曲线，使用蓝色
            ax.plot(time, data, label=f'CarSim_{name}', linewidth=1.5, color='blue')

        # 绘制CSV数据曲线（红色）
        if csv_groups and group_num in csv_groups:
            csv_group_data = csv_groups[group_num]
            for var_data in csv_group_data:
                data = var_data['data']
                name = var_data['name']

                # 创建时间数组
                time = create_time_array(len(data))

                # 绘制CSV曲线，使用红色
                ax.plot(time, data, label=f'PanoSim_{name}', linewidth=1.5, color='red')

        # 设置子图属性
        ax.set_xlabel('时间 (s)')
        ax.set_title(f'组 {group_num}')
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 设置合适的Y轴标签
        if group_data:
            first_var = group_data[0]['name']
            if 'deg' in first_var:
                ax.set_ylabel('角度 (deg)')
            elif 'kmh' in first_var:
                ax.set_ylabel('速度 (km/h)')
            elif 'degs' in first_var:
                ax.set_ylabel('角速度 (deg/s)')
            elif '_N' in first_var:
                ax.set_ylabel('力 (N)')
            else:
                ax.set_ylabel('数值')

    # 隐藏多余的子图
    for idx in range(num_groups, rows * cols):
        row = idx // cols
        col = idx % cols
        axes[row, col].set_visible(False)

    plt.tight_layout()

    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图片已保存到: {save_path}")

    plt.show()

def main():
    """
    主函数
    """
    # 数据目录路径
    data_directory = "CarSim和PanoSim对比/CarSim_120公里初速度，油门开度为0，路面摩擦系数都为1，15秒，DLC开环转向模式"

    # CSV文件路径
    csv_path = "CarSim和PanoSim对比/PanoSim_120公里初速度，油门开度为0，路面摩擦系数都为1，15秒，DLC开环转向模式/vehicle_data.csv"

    # 检查目录是否存在
    if not os.path.exists(data_directory):
        print(f"错误: 目录 '{data_directory}' 不存在")
        return

    print("正在加载CarSim数据文件...")
    data_groups = load_data_files(data_directory)

    if not data_groups:
        print("未找到有效的CarSim数据文件")
        return

    print(f"找到 {len(data_groups)} 个CarSim数据组:")
    for group_num, group_data in sorted(data_groups.items()):
        var_names = [var['name'] for var in group_data]
        print(f"  组 {group_num}: {', '.join(var_names)}")

    # 加载CSV数据
    print("\n正在加载PanoSim CSV数据...")
    csv_groups = load_csv_data(csv_path)

    if csv_groups:
        print(f"找到 {len(csv_groups)} 个PanoSim数据组:")
        for group_num, group_data in sorted(csv_groups.items()):
            var_names = [var['name'] for var in group_data]
            print(f"  组 {group_num}: {', '.join(var_names)}")
    else:
        print("未找到有效的PanoSim CSV数据")

    print("\n正在生成对比图表...")

    # 生成保存路径
    save_path = "CarSim和PanoSim对比_结果图.png"

    # 绘制图表
    plot_comparison_data(data_groups, csv_groups, save_path)

if __name__ == "__main__":
    main()