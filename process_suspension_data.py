#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
悬架弹簧压缩量数据处理程序
该程序读取左悬架弹簧压缩量数据，将每个数值减去128.730010986328125后写入比较文件
"""

import os

def process_suspension_data():
    """
    处理悬架弹簧压缩量数据
    从源文件读取数据，减去基准值后写入目标文件
    """
    # 定义文件路径
    source_file = "验证轮跳/CmpS_L1_左悬架弹簧压缩量.txt"
    target_file = "验证轮跳/比较.txt"
    
    # 基准值
    baseline_value = 128.730010986328125
    
    try:
        # 检查源文件是否存在
        if not os.path.exists(source_file):
            print(f"错误：源文件 '{source_file}' 不存在！")
            return False
        
        # 确保目标目录存在
        target_dir = os.path.dirname(target_file)
        if target_dir and not os.path.exists(target_dir):
            os.makedirs(target_dir)
            print(f"创建目录：{target_dir}")
        
        # 读取源文件并处理数据
        processed_data = []
        line_count = 0
        
        print(f"开始处理文件：{source_file}")
        print(f"基准值：{baseline_value}")
        
        with open(source_file, 'r', encoding='utf-8') as infile:
            for line in infile:
                line = line.strip()
                if line:  # 跳过空行
                    try:
                        # 将字符串转换为浮点数
                        original_value = float(line)
                        # 减去基准值
                        processed_value = original_value - baseline_value
                        processed_data.append(processed_value)
                        line_count += 1
                        
                        # 每处理1000行显示一次进度
                        if line_count % 1000 == 0:
                            print(f"已处理 {line_count} 行数据...")
                            
                    except ValueError:
                        print(f"警告：第 {line_count + 1} 行数据格式错误，跳过：{line}")
                        continue
        
        # 写入处理后的数据到目标文件
        print(f"开始写入处理后的数据到：{target_file}")

        with open(target_file, 'w', encoding='utf-8') as outfile:
            for i, value in enumerate(processed_data):
                # 保持原有的精度格式
                outfile.write(f"{value:.15f}\n")
                # 每写入1000行显示一次进度
                if (i + 1) % 1000 == 0:
                    print(f"已写入 {i + 1} 行数据...")
            # 确保数据被写入磁盘
            outfile.flush()
        
        print(f"数据处理完成！")
        print(f"总共处理了 {line_count} 行数据")
        print(f"结果已保存到：{target_file}")
        
        # 显示前几行和后几行的处理结果作为验证
        if processed_data:
            print("\n处理结果预览：")
            print("前5行数据：")
            for i in range(min(5, len(processed_data))):
                print(f"  第{i+1}行: {processed_data[i]:.15f}")
            
            if len(processed_data) > 10:
                print("后5行数据：")
                for i in range(max(0, len(processed_data)-5), len(processed_data)):
                    print(f"  第{i+1}行: {processed_data[i]:.15f}")
        
        return True
        
    except FileNotFoundError:
        print(f"错误：无法找到文件 '{source_file}'")
        return False
    except PermissionError:
        print(f"错误：没有权限访问文件")
        return False
    except Exception as e:
        print(f"处理过程中发生错误：{str(e)}")
        return False

def main():
    """
    主函数
    """
    print("=" * 60)
    print("悬架弹簧压缩量数据处理程序")
    print("=" * 60)
    
    success = process_suspension_data()
    
    if success:
        print("\n程序执行成功！")
    else:
        print("\n程序执行失败！")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
