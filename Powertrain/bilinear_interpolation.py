#!/usr/bin/env python3
"""
双线性插值计算引擎扭矩
====================

根据 Powertrain/Engine.py 的MAP图，使用双线性插值计算
当 rpm=3127.5，throttle=0.18 时的扭矩值。

Author: Generated for Engine MAP interpolation
Date: 2025-01-05
"""

import numpy as np
import sys
import os

# 添加Powertrain目录到路径
sys.path.append('Powertrain')

def bilinear_interpolation(x, y, x_array, y_array, z_matrix):
    """
    双线性插值函数
    
    参数:
    x: 目标x值 (rpm)
    y: 目标y值 (throttle)
    x_array: x轴数据点 (rpm数组)
    y_array: y轴数据点 (throttle数组)
    z_matrix: 二维数据矩阵 (torque矩阵)
    
    返回:
    插值结果
    """
    
    # 检查输入是否在范围内
    if x < x_array[0] or x > x_array[-1]:
        raise ValueError(f"RPM {x} 超出范围 [{x_array[0]}, {x_array[-1]}]")
    if y < y_array[0] or y > y_array[-1]:
        raise ValueError(f"Throttle {y} 超出范围 [{y_array[0]}, {y_array[-1]}]")
    
    # 找到x方向的插值点
    x_idx = np.searchsorted(x_array, x) - 1
    if x_idx < 0:
        x_idx = 0
    elif x_idx >= len(x_array) - 1:
        x_idx = len(x_array) - 2
    
    # 找到y方向的插值点
    y_idx = np.searchsorted(y_array, y) - 1
    if y_idx < 0:
        y_idx = 0
    elif y_idx >= len(y_array) - 1:
        y_idx = len(y_array) - 2
    
    # 获取四个角点
    x1, x2 = x_array[x_idx], x_array[x_idx + 1]
    y1, y2 = y_array[y_idx], y_array[y_idx + 1]
    
    # 获取四个角点的z值
    z11 = z_matrix[x_idx, y_idx]      # (x1, y1)
    z12 = z_matrix[x_idx, y_idx + 1]  # (x1, y2)
    z21 = z_matrix[x_idx + 1, y_idx]  # (x2, y1)
    z22 = z_matrix[x_idx + 1, y_idx + 1]  # (x2, y2)
    
    # 计算权重
    wx = (x - x1) / (x2 - x1)
    wy = (y - y1) / (y2 - y1)
    
    # 双线性插值
    # 先在x方向插值
    z_x1 = z11 * (1 - wy) + z12 * wy  # 在x1处的y方向插值
    z_x2 = z21 * (1 - wy) + z22 * wy  # 在x2处的y方向插值
    
    # 再在y方向插值
    z_result = z_x1 * (1 - wx) + z_x2 * wx
    
    return z_result, (x_idx, y_idx), (x1, x2, y1, y2), (z11, z12, z21, z22)

def main():
    """
    主函数：计算指定RPM和throttle下的扭矩值
    """
    
    # 从Engine.py导入数据
    try:
        from Engine import rpm, throttle, torque
        print("成功导入Engine.py数据")
    except ImportError:
        print("无法导入Engine.py，使用本地数据")
        # 如果无法导入，使用本地数据
        rpm = np.array([0, 400, 517, 633, 750, 1107, 1463, 1820, 2177, 2533, 2890, 3247, 3603, 3960, 4317, 4673, 5030, 5387, 5743, 6100, 6500, 6700])
        throttle = np.array([0, 0.1, 0.15, 0.2, 0.35, 0.5, 0.7, 0.85, 0.95, 1])
        torque = np.array([[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                          [24.75, 76.11, 101.37, 103.43, 104.6, 105.18, 105.38, 105.43, 105.46, 105.43],
                          [29.7, 92.87, 139.66, 149.15, 150.67, 151.28, 151.5, 151.52, 151.52, 151.52],
                          [0, 87.39, 148.02, 161.35, 164.95, 166.25, 166.73, 166.77, 166.77, 166.75],
                          [-9.9, 70.62, 173.59, 198.66, 208.61, 212.02, 213.29, 213.4, 213.37, 213.29],
                          [-14.85, 47.12, 167.31, 207.9, 221.15, 227.15, 230.09, 230.77, 231.22, 235.62],
                          [-16.83, 25.74, 148.5, 211.6, 233.68, 242.29, 246.89, 248.14, 249.06, 255.42],
                          [-20.17, 9.9, 117.65, 203.67, 237.6, 257.4, 260.37, 263.34, 266.31, 272.88],
                          [-25.32, -6.41, 81.3, 182.08, 239.58, 267, 274.23, 279.24, 282.15, 287.1],
                          [-30.14, -15.16, 56.41, 153.5, 233.12, 269.9, 285.22, 289.08, 295.02, 299.97],
                          [-34.39, -22.06, 37.66, 122.49, 223.45, 267.3, 287.1, 295.02, 300.96, 306.9],
                          [-38.57, -28.1, 23.48, 96.53, 201.04, 259.38, 288.09, 297, 303.93, 309.87],
                          [-42.77, -33.71, 11.8, 76.42, 174.35, 248.62, 285.12, 296.01, 302.94, 306.9],
                          [-47.21, -39.38, 0.28, 57.45, 150.24, 237.3, 280.17, 290.07, 297, 302.88],
                          [-51.83, -45.01, -9.97, 41.19, 125.06, 220.31, 272.25, 282.15, 292.05, 297],
                          [-56.74, -50.57, -19.08, 26.99, 102.65, 198.96, 262.35, 272.25, 282.15, 287.1],
                          [-61.67, -56.24, -27.53, 14.45, 83.52, 176.58, 246.84, 256.41, 269.64, 272.49],
                          [-67.19, -62.17, -35.72, 2.82, 66.2, 153.28, 221.41, 236.52, 243.61, 246.41],
                          [-73.21, -68.41, -43.88, -8.16, 50.38, 130.66, 193.86, 207.92, 214.52, 217.05],
                          [-80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19],
                          [-81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18]])
    
    # 目标值
    target_rpm = 3127.5
    target_throttle = 0.18
    
    print(f"\n=== 双线性插值计算 ===")
    print(f"目标RPM: {target_rpm}")
    print(f"目标Throttle: {target_throttle}")
    
    # 显示数据范围
    print(f"\nRPM范围: {rpm[0]} - {rpm[-1]}")
    print(f"Throttle范围: {throttle[0]} - {throttle[-1]}")
    print(f"Torque矩阵形状: {torque.shape}")
    
    try:
        # 执行双线性插值
        result, indices, corners, z_values = bilinear_interpolation(
            target_rpm, target_throttle, rpm, throttle, torque
        )
        
        x_idx, y_idx = indices
        x1, x2, y1, y2 = corners
        z11, z12, z21, z22 = z_values
        
        print(f"\n=== 插值详细信息 ===")
        print(f"RPM插值区间: [{x1}, {x2}] (索引: {x_idx}, {x_idx+1})")
        print(f"Throttle插值区间: [{y1}, {y2}] (索引: {y_idx}, {y_idx+1})")
        
        print(f"\n四个角点的扭矩值:")
        print(f"  ({x1:.1f}, {y1:.2f}) -> {z11:.2f} Nm")
        print(f"  ({x1:.1f}, {y2:.2f}) -> {z12:.2f} Nm")
        print(f"  ({x2:.1f}, {y1:.2f}) -> {z21:.2f} Nm")
        print(f"  ({x2:.1f}, {y2:.2f}) -> {z22:.2f} Nm")
        
        print(f"\n=== 插值结果 ===")
        print(f"当RPM = {target_rpm}, Throttle = {target_throttle}时")
        print(f"扭矩 = {result:.2f} Nm")
        
        # 验证：使用scipy的RegularGridInterpolator进行对比
        try:
            from scipy.interpolate import RegularGridInterpolator
            interpolator = RegularGridInterpolator((rpm, throttle), torque, method='linear')
            scipy_result = interpolator([target_rpm, target_throttle])[0]

            print(f"\n=== 验证 ===")
            print(f"SciPy插值结果: {scipy_result:.2f} Nm")
            print(f"差异: {abs(result - scipy_result):.6f} Nm")
        except ImportError:
            print(f"\n=== 验证 ===")
            print("SciPy不可用，跳过验证")
        
    except Exception as e:
        print(f"插值计算出错: {e}")
        return
    
    print(f"\n计算完成！")

if __name__ == "__main__":
    main()
