#!/usr/bin/env python3
"""
Fx vs Kappa Analysis Tool
========================

This script provides analysis tools for the Fx vs Kappa sweep data.
It can load existing data or generate new sweep data.

Author: Generated for MFPy analysis
Date: 2025-01-05
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import mfpy
import os
import pandas as pd

def analyze_fx_kappa_data(csv_file):
    """
    Analyze existing Fx vs Kappa data from CSV file
    """
    try:
        # Load data
        data = pd.read_csv(csv_file)
        
        print(f"Loaded data from: {csv_file}")
        print(f"Number of data points: {len(data)}")
        
        # Extract data
        kappa = data['Slip_Ratio_[%]'].values
        fx = data['Fx_[N]'].values
        mu_x = data['muX_[-]'].values
        fz = data['Fz_[N]'].values[0]  # Should be constant
        speed = data['Speed_[km/h]'].values[0]  # Should be constant
        
        # Analysis
        print(f"\n=== ANALYSIS RESULTS ===")
        print(f"Vertical Load (Fz): {fz:.0f} N")
        print(f"Speed: {speed:.1f} km/h")
        print(f"Slip ratio range: {kappa.min():.1f}% to {kappa.max():.1f}%")
        
        # Find key points
        max_fx_idx = np.argmax(np.abs(fx))
        max_fx = fx[max_fx_idx]
        max_fx_kappa = kappa[max_fx_idx]
        
        print(f"\nMaximum |Fx|: {abs(max_fx):.1f} N at κ = {max_fx_kappa:.1f}%")
        print(f"Maximum friction coefficient: {mu_x.max():.3f}")
        
        # Find zero crossing (around κ = 0)
        zero_idx = np.argmin(np.abs(kappa))
        print(f"At κ ≈ 0%: Fx = {fx[zero_idx]:.2e} N")
        
        # Calculate stiffness around zero
        # Find points around ±1%
        idx_neg1 = np.argmin(np.abs(kappa + 1))
        idx_pos1 = np.argmin(np.abs(kappa - 1))
        
        if idx_pos1 != idx_neg1:
            stiffness = (fx[idx_pos1] - fx[idx_neg1]) / (kappa[idx_pos1] - kappa[idx_neg1]) * 100
            print(f"Longitudinal stiffness (±1%): {stiffness:.0f} N per unit slip ratio")
        
        # Asymmetry analysis
        pos_max_fx = np.max(fx[kappa > 0])
        neg_min_fx = np.min(fx[kappa < 0])
        asymmetry = (pos_max_fx + neg_min_fx) / (pos_max_fx - neg_min_fx) * 100
        print(f"Force asymmetry: {asymmetry:.2f}%")
        
        return {
            'kappa': kappa,
            'fx': fx,
            'mu_x': mu_x,
            'fz': fz,
            'speed': speed,
            'max_fx': max_fx,
            'max_fx_kappa': max_fx_kappa,
            'stiffness': stiffness if 'stiffness' in locals() else None
        }
        
    except Exception as e:
        print(f"Error analyzing data: {e}")
        return None

def create_detailed_plot(analysis_data, output_file=None):
    """
    Create detailed analysis plots
    """
    if analysis_data is None:
        return
    
    kappa = analysis_data['kappa']
    fx = analysis_data['fx']
    mu_x = analysis_data['mu_x']
    fz = analysis_data['fz']
    speed = analysis_data['speed']
    
    # Create figure with 3 subplots (removed friction coefficient plot)
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))

    # Plot 1: Full range Fx vs Kappa
    ax1.plot(kappa, fx, 'b-', linewidth=2)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('Slip Ratio [%]')
    ax1.set_ylabel('Longitudinal Force Fx [N]')
    ax1.set_title(f'Fx vs Slip Ratio (Full Range)\nFz = {fz:.0f} N, Speed = {speed:.1f} km/h')
    ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax1.axvline(x=0, color='k', linestyle='--', alpha=0.5)

    # Plot 2: Zoomed view around zero
    zoom_range = 10  # ±10%
    zoom_mask = (kappa >= -zoom_range) & (kappa <= zoom_range)
    ax2.plot(kappa[zoom_mask], fx[zoom_mask], 'r-', linewidth=2, marker='o', markersize=3)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlabel('Slip Ratio [%]')
    ax2.set_ylabel('Longitudinal Force Fx [N]')
    ax2.set_title(f'Fx vs Slip Ratio (±{zoom_range}% Range)')
    ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax2.axvline(x=0, color='k', linestyle='--', alpha=0.5)

    # Plot 3: Fx/Fz ratio (normalized force)
    fx_normalized = fx / fz
    ax3.plot(kappa, fx_normalized, 'm-', linewidth=2)
    ax3.grid(True, alpha=0.3)
    ax3.set_xlabel('Slip Ratio [%]')
    ax3.set_ylabel('Normalized Force Fx/Fz [-]')
    ax3.set_title('Normalized Longitudinal Force')
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax3.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Detailed analysis plot saved as: {output_file}")
    
    return fig

def main():
    """
    Main analysis function
    """
    print("=== Fx vs Kappa Analysis Tool ===\n")
    
    # Check if sweep data exists
    csv_file = "fx_kappa_sweep_Fz4000N_10kmh.csv"
    
    if os.path.exists(csv_file):
        print(f"Found existing data file: {csv_file}")
        
        # Analyze existing data
        analysis_data = analyze_fx_kappa_data(csv_file)
        
        if analysis_data:
            # Create detailed plots
            plot_file = "fx_kappa_detailed_analysis.png"
            create_detailed_plot(analysis_data, plot_file)
            
            print(f"\n=== FILES GENERATED ===")
            print(f"Detailed analysis plot: {plot_file}")
    else:
        print(f"Data file {csv_file} not found.")
        print("Please run fx_kappa_sweep.py first to generate the data.")
    
    print("\nAnalysis completed!")

if __name__ == "__main__":
    main()
