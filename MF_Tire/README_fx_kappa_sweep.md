# Fx vs Kappa Sweep Analysis

This directory contains Python scripts and results for analyzing the longitudinal force (Fx) vs slip ratio (kappa) relationship using the Pacejka tire model.

## Files Generated

### Python Scripts
- **`fx_kappa_sweep.py`** - Main sweep script that generates Fx vs Kappa data
- **`fx_kappa_analysis.py`** - Analysis script that processes the sweep data and creates detailed plots

### Data Files
- **`fx_kappa_sweep_Fz4000N_10kmh.csv`** - Raw sweep data in CSV format
- **`fx_kappa_sweep_Fz4000N_10kmh.png`** - Basic plots (Fx vs Kappa and μx vs Kappa)
- **`fx_kappa_detailed_analysis.png`** - Detailed analysis plots with 4 subplots

## Simulation Parameters

- **Vertical Load (Fz)**: 4000 N
- **Speed**: 10 km/h (2.78 m/s)
- **Slip Ratio Range**: -100% to +100%
- **Step Size**: 1% (201 data points)
- **Slip <PERSON>le (α)**: 0° (pure longitudinal slip)
- **Camber Angle (γ)**: 0°

## Key Results

### Force Characteristics
- **Maximum |Fx|**: 3999.8 N at κ = -16.0%
- **Maximum Friction Coefficient**: 1.000
- **Force at κ ≈ 0%**: 4.46e-11 N (essentially zero)
- **Force Asymmetry**: 0.00% (perfectly symmetric)

### Tire Stiffness
- **Longitudinal Stiffness (±1%)**: 50,036 N per unit slip ratio
- This means for every 1% increase in slip ratio, the force increases by ~500 N

### Tire Parameters Used
- **Tire Nominal Load (FNOMIN)**: 3000 N
- **Tire Unloaded Radius**: 0.3 m
- **Tire Model**: Pacejka 5.2 (Magic Formula)

## Plot Descriptions

### Basic Plot (`fx_kappa_sweep_Fz4000N_10kmh.png`)
1. **Top**: Fx vs Slip Ratio - Shows the characteristic S-curve of tire force
2. **Bottom**: Friction Coefficient vs Slip Ratio - Shows μx behavior

### Detailed Analysis Plot (`fx_kappa_detailed_analysis.png`)
1. **Top Left**: Full range Fx vs Kappa (-100% to +100%)
2. **Top Right**: Zoomed view around zero (±10% range)
3. **Bottom Left**: Friction coefficient μx vs Kappa
4. **Bottom Right**: Normalized force Fx/Fz vs Kappa

## Usage

### To Generate New Sweep Data:
```bash
python fx_kappa_sweep.py
```

### To Analyze Existing Data:
```bash
python fx_kappa_analysis.py
```

## Data Format (CSV)

The CSV file contains the following columns:
- `Slip_Ratio_[%]` - Slip ratio in percentage
- `Fx_[N]` - Longitudinal force in Newtons
- `muX_[-]` - Friction coefficient (dimensionless)
- `Fz_[N]` - Vertical load in Newtons (constant)
- `Speed_[km/h]` - Vehicle speed in km/h (constant)

## Technical Notes

### Tire Model
- Uses the Pacejka Magic Formula 5.2 implementation
- All scaling coefficients set to 1.0 (standard conditions)
- Road rolling resistance coefficient (LMY) = 1
- Road friction coefficients (LMUX, LMUY) = 1

### Calculation Method
- Pure longitudinal slip condition (α = 0, γ = 0)
- Combined slip effects included in the model
- No limit checking applied to allow full range analysis

### Performance
- 201 data points calculated in < 1 second
- Memory efficient implementation
- Non-interactive plotting for batch processing

## Interpretation

The results show typical tire behavior:
1. **Linear region** around κ = 0% with high stiffness
2. **Peak force** occurs around κ = ±16%
3. **Saturation region** at high slip ratios
4. **Perfect symmetry** between positive and negative slip

This data can be used for:
- Vehicle dynamics simulation
- Tire model validation
- Control system design
- Performance analysis

## Dependencies

- numpy
- matplotlib
- scipy
- pandas
- mfpy (local package)

## Author

Generated for MFPy tire analysis - 2025-01-05
