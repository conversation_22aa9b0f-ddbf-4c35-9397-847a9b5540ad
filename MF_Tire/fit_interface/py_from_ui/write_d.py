# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'write_design.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.setWindowModality(QtCore.Qt.ApplicationModal)
        MainWindow.resize(1009, 717)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.tabWidget = QtWidgets.QTabWidget(self.centralwidget)
        self.tabWidget.setTabPosition(QtWidgets.QTabWidget.North)
        self.tabWidget.setUsesScrollButtons(True)
        self.tabWidget.setTabBarAutoHide(False)
        self.tabWidget.setObjectName("tabWidget")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.tab)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.mdi_header = QtWidgets.QTableWidget(self.tab)
        self.mdi_header.setRowCount(4)
        self.mdi_header.setColumnCount(1)
        self.mdi_header.setObjectName("mdi_header")
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.mdi_header.setItem(3, 0, item)
        self.mdi_header.horizontalHeader().setStretchLastSection(True)
        self.mdi_header.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_4.addWidget(self.mdi_header)
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.tab_2)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.units = QtWidgets.QTableWidget(self.tab_2)
        self.units.setRowCount(5)
        self.units.setColumnCount(1)
        self.units.setObjectName("units")
        item = QtWidgets.QTableWidgetItem()
        self.units.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.units.setItem(4, 0, item)
        self.units.horizontalHeader().setStretchLastSection(True)
        self.units.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_5.addWidget(self.units)
        self.tabWidget.addTab(self.tab_2, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.tab_3)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.model = QtWidgets.QTableWidget(self.tab_3)
        self.model.setRowCount(5)
        self.model.setColumnCount(1)
        self.model.setObjectName("model")
        item = QtWidgets.QTableWidgetItem()
        self.model.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.model.setItem(4, 0, item)
        self.model.horizontalHeader().setStretchLastSection(True)
        self.model.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_6.addWidget(self.model)
        self.tabWidget.addTab(self.tab_3, "")
        self.tab_6 = QtWidgets.QWidget()
        self.tab_6.setObjectName("tab_6")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout(self.tab_6)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.dimension = QtWidgets.QTableWidget(self.tab_6)
        self.dimension.setRowCount(5)
        self.dimension.setColumnCount(1)
        self.dimension.setObjectName("dimension")
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.dimension.setItem(4, 0, item)
        self.dimension.horizontalHeader().setStretchLastSection(True)
        self.dimension.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_14.addWidget(self.dimension)
        self.tabWidget.addTab(self.tab_6, "")
        self.tab_9 = QtWidgets.QWidget()
        self.tab_9.setObjectName("tab_9")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.tab_9)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.vertical = QtWidgets.QTableWidget(self.tab_9)
        self.vertical.setRowCount(6)
        self.vertical.setColumnCount(1)
        self.vertical.setObjectName("vertical")
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical.setItem(5, 0, item)
        self.vertical.horizontalHeader().setStretchLastSection(True)
        self.vertical.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_8.addWidget(self.vertical)
        self.tabWidget.addTab(self.tab_9, "")
        self.tab_10 = QtWidgets.QWidget()
        self.tab_10.setObjectName("tab_10")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.tab_10)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.long_slip_range = QtWidgets.QTableWidget(self.tab_10)
        self.long_slip_range.setRowCount(2)
        self.long_slip_range.setColumnCount(1)
        self.long_slip_range.setObjectName("long_slip_range")
        item = QtWidgets.QTableWidgetItem()
        self.long_slip_range.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.long_slip_range.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.long_slip_range.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.long_slip_range.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.long_slip_range.setItem(1, 0, item)
        self.long_slip_range.horizontalHeader().setStretchLastSection(True)
        self.long_slip_range.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_9.addWidget(self.long_slip_range)
        self.tabWidget.addTab(self.tab_10, "")
        self.tab_11 = QtWidgets.QWidget()
        self.tab_11.setObjectName("tab_11")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.tab_11)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.slip_angle_range = QtWidgets.QTableWidget(self.tab_11)
        self.slip_angle_range.setRowCount(2)
        self.slip_angle_range.setColumnCount(1)
        self.slip_angle_range.setObjectName("slip_angle_range")
        item = QtWidgets.QTableWidgetItem()
        self.slip_angle_range.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.slip_angle_range.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.slip_angle_range.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.slip_angle_range.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.slip_angle_range.setItem(1, 0, item)
        self.slip_angle_range.horizontalHeader().setStretchLastSection(True)
        self.slip_angle_range.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_10.addWidget(self.slip_angle_range)
        self.tabWidget.addTab(self.tab_11, "")
        self.tab_12 = QtWidgets.QWidget()
        self.tab_12.setObjectName("tab_12")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.tab_12)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.inclination_angle_range = QtWidgets.QTableWidget(self.tab_12)
        self.inclination_angle_range.setRowCount(2)
        self.inclination_angle_range.setColumnCount(1)
        self.inclination_angle_range.setObjectName("inclination_angle_range")
        item = QtWidgets.QTableWidgetItem()
        self.inclination_angle_range.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.inclination_angle_range.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.inclination_angle_range.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.inclination_angle_range.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.inclination_angle_range.setItem(1, 0, item)
        self.inclination_angle_range.horizontalHeader().setStretchLastSection(True)
        self.inclination_angle_range.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_11.addWidget(self.inclination_angle_range)
        self.tabWidget.addTab(self.tab_12, "")
        self.tab_13 = QtWidgets.QWidget()
        self.tab_13.setObjectName("tab_13")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.tab_13)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.vertical_force_range = QtWidgets.QTableWidget(self.tab_13)
        self.vertical_force_range.setRowCount(2)
        self.vertical_force_range.setColumnCount(1)
        self.vertical_force_range.setObjectName("vertical_force_range")
        item = QtWidgets.QTableWidgetItem()
        self.vertical_force_range.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical_force_range.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical_force_range.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical_force_range.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.vertical_force_range.setItem(1, 0, item)
        self.vertical_force_range.horizontalHeader().setStretchLastSection(True)
        self.vertical_force_range.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_12.addWidget(self.vertical_force_range)
        self.tabWidget.addTab(self.tab_13, "")
        self.tab_14 = QtWidgets.QWidget()
        self.tab_14.setObjectName("tab_14")
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout(self.tab_14)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.scaling = QtWidgets.QTableWidget(self.tab_14)
        self.scaling.setRowCount(25)
        self.scaling.setColumnCount(1)
        self.scaling.setObjectName("scaling")
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(17, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(18, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(19, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(20, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(21, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(22, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(23, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setVerticalHeaderItem(24, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(6, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(7, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(8, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(9, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(10, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(11, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(12, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(13, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(14, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(15, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(16, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(17, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(18, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(19, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(20, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(21, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(22, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(23, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.scaling.setItem(24, 0, item)
        self.scaling.horizontalHeader().setStretchLastSection(True)
        self.scaling.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_13.addWidget(self.scaling)
        self.tabWidget.addTab(self.tab_14, "")
        self.longi_tab = QtWidgets.QWidget()
        self.longi_tab.setObjectName("longi_tab")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.longi_tab)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.longitudinal = QtWidgets.QTableWidget(self.longi_tab)
        self.longitudinal.setRowCount(21)
        self.longitudinal.setColumnCount(1)
        self.longitudinal.setObjectName("longitudinal")
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(17, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(18, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(19, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setVerticalHeaderItem(20, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(6, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(7, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(8, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(9, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(10, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(11, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(12, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(13, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(14, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(15, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(16, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(17, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(18, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(19, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.longitudinal.setItem(20, 0, item)
        self.longitudinal.horizontalHeader().setStretchLastSection(True)
        self.longitudinal.verticalHeader().setSortIndicatorShown(False)
        self.longitudinal.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_2.addWidget(self.longitudinal)
        self.tabWidget.addTab(self.longi_tab, "")
        self.lat_tab = QtWidgets.QWidget()
        self.lat_tab.setObjectName("lat_tab")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.lat_tab)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.lateral = QtWidgets.QTableWidget(self.lat_tab)
        self.lateral.setRowCount(32)
        self.lateral.setColumnCount(1)
        self.lateral.setObjectName("lateral")
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(17, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(18, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(19, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(20, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(21, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(22, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(23, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(24, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(25, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(26, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(27, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(28, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(29, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(30, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setVerticalHeaderItem(31, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(6, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(7, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(8, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(9, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(10, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(11, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(12, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(13, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(14, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(15, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(16, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(17, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(18, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(19, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(20, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(21, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(22, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(23, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(24, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(25, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(26, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(27, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(28, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(29, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(30, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.lateral.setItem(31, 0, item)
        self.lateral.horizontalHeader().setStretchLastSection(True)
        self.lateral.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout.addWidget(self.lateral)
        self.tabWidget.addTab(self.lat_tab, "")
        self.ali_tab = QtWidgets.QWidget()
        self.ali_tab.setObjectName("ali_tab")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.ali_tab)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.aligning = QtWidgets.QTableWidget(self.ali_tab)
        self.aligning.setRowCount(29)
        self.aligning.setColumnCount(1)
        self.aligning.setObjectName("aligning")
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(17, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(18, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(19, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(20, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(21, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(22, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(23, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(24, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(25, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(26, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(27, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setVerticalHeaderItem(28, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(4, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(5, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(6, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(7, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(8, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(9, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(10, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(11, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(12, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(13, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(14, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(15, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(16, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(17, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(18, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(19, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(20, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(21, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(22, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(23, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(24, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(25, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(26, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(27, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.aligning.setItem(28, 0, item)
        self.aligning.horizontalHeader().setStretchLastSection(True)
        self.aligning.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_3.addWidget(self.aligning)
        self.tabWidget.addTab(self.ali_tab, "")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout(self.tab_4)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.overturning = QtWidgets.QTableWidget(self.tab_4)
        self.overturning.setRowCount(3)
        self.overturning.setColumnCount(1)
        self.overturning.setObjectName("overturning")
        item = QtWidgets.QTableWidgetItem()
        self.overturning.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.overturning.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.overturning.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.overturning.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.overturning.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.overturning.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.overturning.setItem(2, 0, item)
        self.overturning.horizontalHeader().setStretchLastSection(True)
        self.overturning.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_15.addWidget(self.overturning)
        self.tabWidget.addTab(self.tab_4, "")
        self.tab_5 = QtWidgets.QWidget()
        self.tab_5.setObjectName("tab_5")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout(self.tab_5)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.rolling = QtWidgets.QTableWidget(self.tab_5)
        self.rolling.setRowCount(4)
        self.rolling.setColumnCount(1)
        self.rolling.setObjectName("rolling")
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.rolling.setItem(3, 0, item)
        self.rolling.horizontalHeader().setStretchLastSection(True)
        self.rolling.verticalHeader().setStretchLastSection(True)
        self.horizontalLayout_16.addWidget(self.rolling)
        self.tabWidget.addTab(self.tab_5, "")
        self.verticalLayout.addWidget(self.tabWidget)
        self.write_button = QtWidgets.QPushButton(self.centralwidget)
        self.write_button.setObjectName("write_button")
        self.verticalLayout.addWidget(self.write_button)
        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MFPy Fit - write .tir"))
        item = self.mdi_header.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "FILE_TYPE"))
        item = self.mdi_header.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "FILE_VERSION"))
        item = self.mdi_header.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "FILE_FORMAT"))
        item = self.mdi_header.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "! : Comment:"))
        item = self.mdi_header.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.mdi_header.isSortingEnabled()
        self.mdi_header.setSortingEnabled(False)
        item = self.mdi_header.item(0, 0)
        item.setText(_translate("MainWindow", "\'tir\'"))
        item = self.mdi_header.item(1, 0)
        item.setText(_translate("MainWindow", "1.0"))
        item = self.mdi_header.item(2, 0)
        item.setText(_translate("MainWindow", "\'ASCII\'"))
        item = self.mdi_header.item(3, 0)
        item.setText(_translate("MainWindow", "\'Fit using MFPy\'"))
        self.mdi_header.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("MainWindow", "MDI_HEADER"))
        item = self.units.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "LENGTH"))
        item = self.units.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "FORCE"))
        item = self.units.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "ANGLE"))
        item = self.units.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "MASS"))
        item = self.units.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "TIME"))
        item = self.units.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.units.isSortingEnabled()
        self.units.setSortingEnabled(False)
        item = self.units.item(0, 0)
        item.setText(_translate("MainWindow", "\'meter\'"))
        item = self.units.item(1, 0)
        item.setText(_translate("MainWindow", "\'Newton\'"))
        item = self.units.item(2, 0)
        item.setText(_translate("MainWindow", "\'radians\'"))
        item = self.units.item(3, 0)
        item.setText(_translate("MainWindow", "\'kg\'"))
        item = self.units.item(4, 0)
        item.setText(_translate("MainWindow", "\'second\'"))
        self.units.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("MainWindow", "UNITS"))
        item = self.model.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "FITTYP "))
        item = self.model.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "USE_MODE"))
        item = self.model.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "VXLOW"))
        item = self.model.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "LONGVL"))
        item = self.model.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "TYRESIDE"))
        item = self.model.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.model.isSortingEnabled()
        self.model.setSortingEnabled(False)
        item = self.model.item(0, 0)
        item.setText(_translate("MainWindow", "6"))
        item = self.model.item(1, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.model.item(2, 0)
        item.setText(_translate("MainWindow", "1"))
        item = self.model.item(3, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.model.item(4, 0)
        item.setText(_translate("MainWindow", "\'Left\'"))
        self.model.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("MainWindow", "MODEL"))
        item = self.dimension.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "UNLOADED_RADIUS"))
        item = self.dimension.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "WIDTH"))
        item = self.dimension.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "ASPECT_RATIO"))
        item = self.dimension.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "RIM_RADIUS"))
        item = self.dimension.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "RIM_WIDTH"))
        item = self.dimension.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.dimension.isSortingEnabled()
        self.dimension.setSortingEnabled(False)
        item = self.dimension.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.dimension.item(1, 0)
        item.setText(_translate("MainWindow", "0.2"))
        item = self.dimension.item(2, 0)
        item.setText(_translate("MainWindow", "0.5"))
        item = self.dimension.item(3, 0)
        item.setText(_translate("MainWindow", "0.2"))
        item = self.dimension.item(4, 0)
        item.setText(_translate("MainWindow", "0.2"))
        self.dimension.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_6), _translate("MainWindow", "DIMENSION"))
        item = self.vertical.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "FNOMIN"))
        item = self.vertical.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "VERTICAL_STIFFNESS"))
        item = self.vertical.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "VERTICAL_DAMPING"))
        item = self.vertical.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "BREFF"))
        item = self.vertical.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "DREFF"))
        item = self.vertical.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "FREFF"))
        item = self.vertical.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.vertical.isSortingEnabled()
        self.vertical.setSortingEnabled(False)
        item = self.vertical.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.vertical.item(1, 0)
        item.setText(_translate("MainWindow", "5000"))
        item = self.vertical.item(2, 0)
        item.setText(_translate("MainWindow", "200000"))
        item = self.vertical.item(3, 0)
        item.setText(_translate("MainWindow", "50"))
        item = self.vertical.item(4, 0)
        item.setText(_translate("MainWindow", "10"))
        item = self.vertical.item(5, 0)
        item.setText(_translate("MainWindow", "0.5"))
        self.vertical.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_9), _translate("MainWindow", "VERTICAL"))
        item = self.long_slip_range.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "KPUMIN"))
        item = self.long_slip_range.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "KPUMAX"))
        item = self.long_slip_range.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.long_slip_range.isSortingEnabled()
        self.long_slip_range.setSortingEnabled(False)
        item = self.long_slip_range.item(0, 0)
        item.setText(_translate("MainWindow", "-0.5"))
        item = self.long_slip_range.item(1, 0)
        item.setText(_translate("MainWindow", "0.5"))
        self.long_slip_range.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_10), _translate("MainWindow", "LONG_SLIP_RANGE"))
        item = self.slip_angle_range.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "ALPMIN"))
        item = self.slip_angle_range.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "ALPMAX"))
        item = self.slip_angle_range.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.slip_angle_range.isSortingEnabled()
        self.slip_angle_range.setSortingEnabled(False)
        item = self.slip_angle_range.item(0, 0)
        item.setText(_translate("MainWindow", "-0.2"))
        item = self.slip_angle_range.item(1, 0)
        item.setText(_translate("MainWindow", "0.2"))
        self.slip_angle_range.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_11), _translate("MainWindow", "SLIP_ANGLE_RANGE"))
        item = self.inclination_angle_range.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "CAMMIN"))
        item = self.inclination_angle_range.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "CAMMAX"))
        item = self.inclination_angle_range.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.inclination_angle_range.isSortingEnabled()
        self.inclination_angle_range.setSortingEnabled(False)
        item = self.inclination_angle_range.item(0, 0)
        item.setText(_translate("MainWindow", "-0.1"))
        item = self.inclination_angle_range.item(1, 0)
        item.setText(_translate("MainWindow", "0.1"))
        self.inclination_angle_range.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_12), _translate("MainWindow", "INCLINATION_ANGLE_RANGE"))
        item = self.vertical_force_range.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "FZMIN"))
        item = self.vertical_force_range.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "FZMAX"))
        item = self.vertical_force_range.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.vertical_force_range.isSortingEnabled()
        self.vertical_force_range.setSortingEnabled(False)
        item = self.vertical_force_range.item(0, 0)
        item.setText(_translate("MainWindow", "3000"))
        item = self.vertical_force_range.item(1, 0)
        item.setText(_translate("MainWindow", "10000"))
        self.vertical_force_range.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_13), _translate("MainWindow", "VERTICAL_FORCE_RANGE"))
        item = self.scaling.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "LFZO"))
        item = self.scaling.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "LCX"))
        item = self.scaling.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "LMUX"))
        item = self.scaling.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "LEX"))
        item = self.scaling.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "LKX"))
        item = self.scaling.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "LHX"))
        item = self.scaling.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "LVX"))
        item = self.scaling.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "LGAX"))
        item = self.scaling.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "LCY"))
        item = self.scaling.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "LMUY"))
        item = self.scaling.verticalHeaderItem(10)
        item.setText(_translate("MainWindow", "LEY"))
        item = self.scaling.verticalHeaderItem(11)
        item.setText(_translate("MainWindow", "LKY"))
        item = self.scaling.verticalHeaderItem(12)
        item.setText(_translate("MainWindow", "LHY"))
        item = self.scaling.verticalHeaderItem(13)
        item.setText(_translate("MainWindow", "LVY"))
        item = self.scaling.verticalHeaderItem(14)
        item.setText(_translate("MainWindow", "LGAY"))
        item = self.scaling.verticalHeaderItem(15)
        item.setText(_translate("MainWindow", "LTR"))
        item = self.scaling.verticalHeaderItem(16)
        item.setText(_translate("MainWindow", "LRES"))
        item = self.scaling.verticalHeaderItem(17)
        item.setText(_translate("MainWindow", "LGAZ"))
        item = self.scaling.verticalHeaderItem(18)
        item.setText(_translate("MainWindow", "LMX"))
        item = self.scaling.verticalHeaderItem(19)
        item.setText(_translate("MainWindow", "LVMX"))
        item = self.scaling.verticalHeaderItem(20)
        item.setText(_translate("MainWindow", "LMY"))
        item = self.scaling.verticalHeaderItem(21)
        item.setText(_translate("MainWindow", "LXAL"))
        item = self.scaling.verticalHeaderItem(22)
        item.setText(_translate("MainWindow", "LYKA"))
        item = self.scaling.verticalHeaderItem(23)
        item.setText(_translate("MainWindow", "LVYKA"))
        item = self.scaling.verticalHeaderItem(24)
        item.setText(_translate("MainWindow", "LS"))
        item = self.scaling.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.scaling.isSortingEnabled()
        self.scaling.setSortingEnabled(False)
        item = self.scaling.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(1, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(2, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(3, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(4, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(5, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(6, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(7, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(8, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(9, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(10, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(11, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(12, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(13, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(14, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(15, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(16, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(17, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(18, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(19, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(20, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(21, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(22, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(23, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.scaling.item(24, 0)
        item.setText(_translate("MainWindow", "0"))
        self.scaling.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_14), _translate("MainWindow", "SCALING_COEFFICIENTS"))
        item = self.longitudinal.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "PCX1"))
        item = self.longitudinal.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "PDX1"))
        item = self.longitudinal.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "PDX2"))
        item = self.longitudinal.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "PDX3"))
        item = self.longitudinal.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "PEX1"))
        item = self.longitudinal.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "PEX2"))
        item = self.longitudinal.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "PEX3"))
        item = self.longitudinal.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "PEX4"))
        item = self.longitudinal.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "PKX1"))
        item = self.longitudinal.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "PKX2"))
        item = self.longitudinal.verticalHeaderItem(10)
        item.setText(_translate("MainWindow", "PKX3"))
        item = self.longitudinal.verticalHeaderItem(11)
        item.setText(_translate("MainWindow", "PHX1"))
        item = self.longitudinal.verticalHeaderItem(12)
        item.setText(_translate("MainWindow", "PHX2"))
        item = self.longitudinal.verticalHeaderItem(13)
        item.setText(_translate("MainWindow", "PVX1"))
        item = self.longitudinal.verticalHeaderItem(14)
        item.setText(_translate("MainWindow", "PVX2"))
        item = self.longitudinal.verticalHeaderItem(15)
        item.setText(_translate("MainWindow", "RBX1"))
        item = self.longitudinal.verticalHeaderItem(16)
        item.setText(_translate("MainWindow", "RBX2"))
        item = self.longitudinal.verticalHeaderItem(17)
        item.setText(_translate("MainWindow", "RCX1"))
        item = self.longitudinal.verticalHeaderItem(18)
        item.setText(_translate("MainWindow", "REX1"))
        item = self.longitudinal.verticalHeaderItem(19)
        item.setText(_translate("MainWindow", "REX2"))
        item = self.longitudinal.verticalHeaderItem(20)
        item.setText(_translate("MainWindow", "RHX1"))
        item = self.longitudinal.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.longitudinal.isSortingEnabled()
        self.longitudinal.setSortingEnabled(False)
        item = self.longitudinal.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(1, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(2, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(3, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(4, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(5, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(6, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(7, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(8, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(9, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(10, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(11, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(12, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(13, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(14, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(15, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(16, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(17, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(18, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(19, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.longitudinal.item(20, 0)
        item.setText(_translate("MainWindow", "0"))
        self.longitudinal.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.longi_tab), _translate("MainWindow", "LONGITUDINAL_COEFFICIENTS"))
        item = self.lateral.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "PCY1"))
        item = self.lateral.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "PDY1"))
        item = self.lateral.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "PDY2"))
        item = self.lateral.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "PDY3"))
        item = self.lateral.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "PEY1"))
        item = self.lateral.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "PEY2"))
        item = self.lateral.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "PEY3"))
        item = self.lateral.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "PEY4"))
        item = self.lateral.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "PKY1"))
        item = self.lateral.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "PKY2"))
        item = self.lateral.verticalHeaderItem(10)
        item.setText(_translate("MainWindow", "PKY3"))
        item = self.lateral.verticalHeaderItem(11)
        item.setText(_translate("MainWindow", "PHY1"))
        item = self.lateral.verticalHeaderItem(12)
        item.setText(_translate("MainWindow", "PHY2"))
        item = self.lateral.verticalHeaderItem(13)
        item.setText(_translate("MainWindow", "PHY3"))
        item = self.lateral.verticalHeaderItem(14)
        item.setText(_translate("MainWindow", "PVY1"))
        item = self.lateral.verticalHeaderItem(15)
        item.setText(_translate("MainWindow", "PVY2"))
        item = self.lateral.verticalHeaderItem(16)
        item.setText(_translate("MainWindow", "PVY3"))
        item = self.lateral.verticalHeaderItem(17)
        item.setText(_translate("MainWindow", "PVY4"))
        item = self.lateral.verticalHeaderItem(18)
        item.setText(_translate("MainWindow", "RBY1"))
        item = self.lateral.verticalHeaderItem(19)
        item.setText(_translate("MainWindow", "RBY2"))
        item = self.lateral.verticalHeaderItem(20)
        item.setText(_translate("MainWindow", "RBY3"))
        item = self.lateral.verticalHeaderItem(21)
        item.setText(_translate("MainWindow", "RCY1"))
        item = self.lateral.verticalHeaderItem(22)
        item.setText(_translate("MainWindow", "REY1"))
        item = self.lateral.verticalHeaderItem(23)
        item.setText(_translate("MainWindow", "REY2"))
        item = self.lateral.verticalHeaderItem(24)
        item.setText(_translate("MainWindow", "RHY1"))
        item = self.lateral.verticalHeaderItem(25)
        item.setText(_translate("MainWindow", "RHY2"))
        item = self.lateral.verticalHeaderItem(26)
        item.setText(_translate("MainWindow", "RVY1"))
        item = self.lateral.verticalHeaderItem(27)
        item.setText(_translate("MainWindow", "RVY2"))
        item = self.lateral.verticalHeaderItem(28)
        item.setText(_translate("MainWindow", "RVY3"))
        item = self.lateral.verticalHeaderItem(29)
        item.setText(_translate("MainWindow", "RVY4"))
        item = self.lateral.verticalHeaderItem(30)
        item.setText(_translate("MainWindow", "RVY5"))
        item = self.lateral.verticalHeaderItem(31)
        item.setText(_translate("MainWindow", "RVY6"))
        item = self.lateral.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.lateral.isSortingEnabled()
        self.lateral.setSortingEnabled(False)
        item = self.lateral.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(1, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(2, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(3, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(4, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(5, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(6, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(7, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(8, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(9, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(10, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(11, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(12, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(13, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(14, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(15, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(16, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(17, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(18, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(19, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(20, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(21, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(22, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(23, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(24, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(25, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(26, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(27, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(28, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(29, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(30, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.lateral.item(31, 0)
        item.setText(_translate("MainWindow", "0"))
        self.lateral.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.lat_tab), _translate("MainWindow", "LATERAL_COEFFICIENTS"))
        item = self.aligning.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "QBZ1"))
        item = self.aligning.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "QBZ2"))
        item = self.aligning.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "QBZ3"))
        item = self.aligning.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "QBZ4"))
        item = self.aligning.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "QBZ5"))
        item = self.aligning.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "QBZ9"))
        item = self.aligning.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "QBZ10"))
        item = self.aligning.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "QCZ1"))
        item = self.aligning.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "QDZ1"))
        item = self.aligning.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "QDZ2"))
        item = self.aligning.verticalHeaderItem(10)
        item.setText(_translate("MainWindow", "QDZ3"))
        item = self.aligning.verticalHeaderItem(11)
        item.setText(_translate("MainWindow", "QDZ4"))
        item = self.aligning.verticalHeaderItem(12)
        item.setText(_translate("MainWindow", "QDZ6"))
        item = self.aligning.verticalHeaderItem(13)
        item.setText(_translate("MainWindow", "QDZ7"))
        item = self.aligning.verticalHeaderItem(14)
        item.setText(_translate("MainWindow", "QDZ8"))
        item = self.aligning.verticalHeaderItem(15)
        item.setText(_translate("MainWindow", "QDZ9"))
        item = self.aligning.verticalHeaderItem(16)
        item.setText(_translate("MainWindow", "QEZ1"))
        item = self.aligning.verticalHeaderItem(17)
        item.setText(_translate("MainWindow", "QEZ2"))
        item = self.aligning.verticalHeaderItem(18)
        item.setText(_translate("MainWindow", "QEZ3"))
        item = self.aligning.verticalHeaderItem(19)
        item.setText(_translate("MainWindow", "QEZ4"))
        item = self.aligning.verticalHeaderItem(20)
        item.setText(_translate("MainWindow", "QEZ5"))
        item = self.aligning.verticalHeaderItem(21)
        item.setText(_translate("MainWindow", "QHZ1"))
        item = self.aligning.verticalHeaderItem(22)
        item.setText(_translate("MainWindow", "QHZ2"))
        item = self.aligning.verticalHeaderItem(23)
        item.setText(_translate("MainWindow", "QHZ3"))
        item = self.aligning.verticalHeaderItem(24)
        item.setText(_translate("MainWindow", "QHZ4"))
        item = self.aligning.verticalHeaderItem(25)
        item.setText(_translate("MainWindow", "SSZ1"))
        item = self.aligning.verticalHeaderItem(26)
        item.setText(_translate("MainWindow", "SSZ2"))
        item = self.aligning.verticalHeaderItem(27)
        item.setText(_translate("MainWindow", "SSZ3"))
        item = self.aligning.verticalHeaderItem(28)
        item.setText(_translate("MainWindow", "SSZ4"))
        item = self.aligning.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.aligning.isSortingEnabled()
        self.aligning.setSortingEnabled(False)
        item = self.aligning.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(1, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(2, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(3, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(4, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(5, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(6, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(7, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(8, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(9, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(10, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(11, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(12, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(13, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(14, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(15, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(16, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(17, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(18, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(19, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(20, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(21, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(22, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(23, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(24, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(25, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(26, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(27, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.aligning.item(28, 0)
        item.setText(_translate("MainWindow", "0"))
        self.aligning.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.ali_tab), _translate("MainWindow", "ALIGNING_COEFFICIENTS"))
        item = self.overturning.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "QSX1"))
        item = self.overturning.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "QSX2"))
        item = self.overturning.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "QSX3"))
        item = self.overturning.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.overturning.isSortingEnabled()
        self.overturning.setSortingEnabled(False)
        item = self.overturning.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.overturning.item(1, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.overturning.item(2, 0)
        item.setText(_translate("MainWindow", "0"))
        self.overturning.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), _translate("MainWindow", "OVERTURNING_COEFFICIENTS"))
        item = self.rolling.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "QSY1"))
        item = self.rolling.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "QSY2"))
        item = self.rolling.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "QSY3"))
        item = self.rolling.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "QSY4"))
        item = self.rolling.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "Value"))
        __sortingEnabled = self.rolling.isSortingEnabled()
        self.rolling.setSortingEnabled(False)
        item = self.rolling.item(0, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.rolling.item(1, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.rolling.item(2, 0)
        item.setText(_translate("MainWindow", "0"))
        item = self.rolling.item(3, 0)
        item.setText(_translate("MainWindow", "0"))
        self.rolling.setSortingEnabled(__sortingEnabled)
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_5), _translate("MainWindow", "ROLLING_COEFFICIENTS"))
        self.write_button.setText(_translate("MainWindow", "Write .TIR"))


if __name__ == "__main__":
    import sys
    app = QtWidgets.QApplication(sys.argv)
    MainWindow = QtWidgets.QMainWindow()
    ui = Ui_MainWindow()
    ui.setupUi(MainWindow)
    MainWindow.show()
    sys.exit(app.exec_())
