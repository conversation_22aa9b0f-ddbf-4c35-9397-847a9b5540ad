import os
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import re

def load_data_files(directory_path):
    """
    加载指定目录中的所有txt文件数据
    返回按序号分组的数据字典
    """
    data_groups = defaultdict(list)

    # 获取目录中的所有txt文件
    txt_files = [f for f in os.listdir(directory_path) if f.endswith('.txt')]

    for filename in txt_files:
        # 提取文件名开头的序号
        match = re.match(r'^(\d+)_(.+)\.txt$', filename)
        if match:
            group_num = int(match.group(1))
            variable_name = match.group(2)

            # 读取文件数据
            file_path = os.path.join(directory_path, filename)
            try:
                data = np.loadtxt(file_path)
                data_groups[group_num].append({
                    'name': variable_name,
                    'data': data,
                    'filename': filename
                })
            except Exception as e:
                print(f"Error loading {filename}: {e}")

    return data_groups

def create_time_array(data_length, dt=0.001):
    """
    创建时间数组，假设时间步长为0.001秒
    """
    return np.arange(0, data_length * dt, dt)

def plot_comparison_data(data_groups, save_path=None):
    """
    绘制对比数据图表
    """
    # 计算子图布局
    num_groups = len(data_groups)
    cols = 3  # 每行3个子图
    rows = (num_groups + cols - 1) // cols  # 向上取整

    # 创建图形
    fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
    if rows == 1:
        axes = axes.reshape(1, -1)
    elif cols == 1:
        axes = axes.reshape(-1, 1)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 按序号排序绘制
    sorted_groups = sorted(data_groups.items())

    for idx, (group_num, group_data) in enumerate(sorted_groups):
        row = idx // cols
        col = idx % cols
        ax = axes[row, col]

        # 为每个变量绘制曲线
        for var_data in group_data:
            data = var_data['data']
            name = var_data['name']

            # 创建时间数组
            time = create_time_array(len(data))

            # 绘制曲线
            ax.plot(time, data, label=name, linewidth=1.5)

        # 设置子图属性
        ax.set_xlabel('时间 (s)')
        ax.set_title(f'组 {group_num}')
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 设置合适的Y轴标签
        if group_data:
            first_var = group_data[0]['name']
            if 'deg' in first_var:
                ax.set_ylabel('角度 (deg)')
            elif 'kmh' in first_var:
                ax.set_ylabel('速度 (km/h)')
            elif 'degs' in first_var:
                ax.set_ylabel('角速度 (deg/s)')
            elif '_N' in first_var:
                ax.set_ylabel('力 (N)')
            else:
                ax.set_ylabel('数值')

    # 隐藏多余的子图
    for idx in range(num_groups, rows * cols):
        row = idx // cols
        col = idx % cols
        axes[row, col].set_visible(False)

    plt.tight_layout()

    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图片已保存到: {save_path}")

    plt.show()

def main():
    """
    主函数
    """
    # 数据目录路径
    data_directory = "CarSim和PanoSim对比/120公里初速度，油门开度为0，路面摩擦系数都为1，15秒，DLC开环转向模式"

    # 检查目录是否存在
    if not os.path.exists(data_directory):
        print(f"错误: 目录 '{data_directory}' 不存在")
        return

    print("正在加载数据文件...")
    data_groups = load_data_files(data_directory)

    if not data_groups:
        print("未找到有效的数据文件")
        return

    print(f"找到 {len(data_groups)} 个数据组:")
    for group_num, group_data in sorted(data_groups.items()):
        var_names = [var['name'] for var in group_data]
        print(f"  组 {group_num}: {', '.join(var_names)}")

    print("\n正在生成对比图表...")

    # 生成保存路径
    save_path = "CarSim和PanoSim对比_结果图.png"

    # 绘制图表
    plot_comparison_data(data_groups, save_path)

if __name__ == "__main__":
    main()