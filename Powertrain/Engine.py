import numpy as np
import matplotlib.pyplot as plt

# Engine MAP data
# Note: RPM array should match the number of rows in torque matrix (22 rows)
rpm = np.array([0, 400, 517, 633, 750, 1107, 1463, 1820, 2177, 2533, 2890, 3247, 3603, 3960, 4317, 4673, 5030, 5387, 5743, 6100, 6500, 6700])
throttle = np.array([0, 0.1, 0.15, 0.2, 0.35, 0.5, 0.7, 0.85, 0.95, 1])

torque = np.array([[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                  [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                  [24.75, 76.11, 101.37, 103.43, 104.6, 105.18, 105.38, 105.43, 105.46, 105.43],
                  [29.7, 92.87, 139.66, 149.15, 150.67, 151.28, 151.5, 151.52, 151.52, 151.52],
                  [0, 87.39, 148.02, 161.35, 164.95, 166.25, 166.73, 166.77, 166.77, 166.75],
                  [-9.9, 70.62, 173.59, 198.66, 208.61, 212.02, 213.29, 213.4, 213.37, 213.29],
                  [-14.85, 47.12, 167.31, 207.9, 221.15, 227.15, 230.09, 230.77, 231.22, 235.62],
                  [-16.83, 25.74, 148.5, 211.6, 233.68, 242.29, 246.89, 248.14, 249.06, 255.42],
                  [-20.17, 9.9, 117.65, 203.67, 237.6, 257.4, 260.37, 263.34, 266.31, 272.88],
                  [-25.32, -6.41, 81.3, 182.08, 239.58, 267, 274.23, 279.24, 282.15, 287.1],
                  [-30.14, -15.16, 56.41, 153.5, 233.12, 269.9, 285.22, 289.08, 295.02, 299.97],
                  [-34.39, -22.06, 37.66, 122.49, 223.45, 267.3, 287.1, 295.02, 300.96, 306.9],
                  [-38.57, -28.1, 23.48, 96.53, 201.04, 259.38, 288.09, 297, 303.93, 309.87],
                  [-42.77, -33.71, 11.8, 76.42, 174.35, 248.62, 285.12, 296.01, 302.94, 306.9],
                  [-47.21, -39.38, 0.28, 57.45, 150.24, 237.3, 280.17, 290.07, 297, 302.88],
                  [-51.83, -45.01, -9.97, 41.19, 125.06, 220.31, 272.25, 282.15, 292.05, 297],
                  [-56.74, -50.57, -19.08, 26.99, 102.65, 198.96, 262.35, 272.25, 282.15, 287.1],
                  [-61.67, -56.24, -27.53, 14.45, 83.52, 176.58, 246.84, 256.41, 269.64, 272.49],
                  [-67.19, -62.17, -35.72, 2.82, 66.2, 153.28, 221.41, 236.52, 243.61, 246.41],
                  [-73.21, -68.41, -43.88, -8.16, 50.38, 130.66, 193.86, 207.92, 214.52, 217.05],
                  [-80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19, -80.19],
                  [-81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18, -81.18]
                   ])



def bilinear_interpolation(target_rpm, target_throttle):
    """
    双线性插值函数，根据RPM和throttle计算扭矩

    参数:
    target_rpm: 目标转速 (rpm)
    target_throttle: 目标油门开度 (0-1)

    返回:
    interpolated_torque: 插值得到的扭矩 (Nm)
    """

    # 检查输入是否在范围内
    if target_rpm < rpm[0] or target_rpm > rpm[-1]:
        raise ValueError(f"RPM {target_rpm} 超出范围 [{rpm[0]}, {rpm[-1]}]")
    if target_throttle < throttle[0] or target_throttle > throttle[-1]:
        raise ValueError(f"Throttle {target_throttle} 超出范围 [{throttle[0]}, {throttle[-1]}]")

    # 找到RPM方向的插值点
    rpm_idx = np.searchsorted(rpm, target_rpm) - 1
    if rpm_idx < 0:
        rpm_idx = 0
    elif rpm_idx >= len(rpm) - 1:
        rpm_idx = len(rpm) - 2

    # 找到throttle方向的插值点
    throttle_idx = np.searchsorted(throttle, target_throttle) - 1
    if throttle_idx < 0:
        throttle_idx = 0
    elif throttle_idx >= len(throttle) - 1:
        throttle_idx = len(throttle) - 2

    # 获取四个角点
    rpm1, rpm2 = rpm[rpm_idx], rpm[rpm_idx + 1]
    throttle1, throttle2 = throttle[throttle_idx], throttle[throttle_idx + 1]

    # 获取四个角点的扭矩值
    torque11 = torque[rpm_idx, throttle_idx]      # (rpm1, throttle1)
    torque12 = torque[rpm_idx, throttle_idx + 1]  # (rpm1, throttle2)
    torque21 = torque[rpm_idx + 1, throttle_idx]  # (rpm2, throttle1)
    torque22 = torque[rpm_idx + 1, throttle_idx + 1]  # (rpm2, throttle2)

    # 计算权重
    w_rpm = (target_rpm - rpm1) / (rpm2 - rpm1)
    w_throttle = (target_throttle - throttle1) / (throttle2 - throttle1)

    # 双线性插值
    # 先在throttle方向插值
    torque_rpm1 = torque11 * (1 - w_throttle) + torque12 * w_throttle  # 在rpm1处的throttle方向插值
    torque_rpm2 = torque21 * (1 - w_throttle) + torque22 * w_throttle  # 在rpm2处的throttle方向插值

    # 再在rpm方向插值
    interpolated_torque = torque_rpm1 * (1 - w_rpm) + torque_rpm2 * w_rpm

    return interpolated_torque

def linear_interpolation_2d(target_rpm, target_throttle):
    """
    二维线性插值：分别对RPM和throttle进行插值

    参数:
    target_rpm: 目标转速 (rpm)
    target_throttle: 目标油门开度 (0-1)

    返回:
    interpolated_torque: 插值得到的扭矩 (Nm)
    """

    # 检查输入是否在范围内
    if target_rpm < rpm[0] or target_rpm > rpm[-1]:
        raise ValueError(f"RPM {target_rpm} 超出范围 [{rpm[0]}, {rpm[-1]}]")
    if target_throttle < throttle[0] or target_throttle > throttle[-1]:
        raise ValueError(f"Throttle {target_throttle} 超出范围 [{throttle[0]}, {throttle[-1]}]")

    # 方法1：对每个throttle值，先对RPM进行插值
    interpolated_torques = []
    for i, th in enumerate(throttle):
        torque_at_throttle = torque[:, i]
        interp_torque = np.interp(target_rpm, rpm, torque_at_throttle)
        interpolated_torques.append(interp_torque)

    # 然后对throttle进行插值
    result1 = np.interp(target_throttle, throttle, interpolated_torques)

    # 方法2：对每个RPM值，先对throttle进行插值
    interpolated_torques2 = []
    for i, r in enumerate(rpm):
        torque_at_rpm = torque[i, :]
        interp_torque = np.interp(target_throttle, throttle, torque_at_rpm)
        interpolated_torques2.append(interp_torque)

    # 然后对RPM进行插值
    result2 = np.interp(target_rpm, rpm, interpolated_torques2)

    return result1, result2, (result1 + result2) / 2





# 默认使用双线性插值计算扭矩
def get_torque(target_rpm, target_throttle):
    """
    根据RPM和throttle计算扭矩（默认使用双线性插值）

    参数:
    target_rpm: 目标转速 (rpm)
    target_throttle: 目标油门开度 (0-1)

    返回:
    torque_value: 扭矩值 (Nm)
    """
    return bilinear_interpolation(target_rpm, target_throttle)

def load_data_from_files():
    """
    从AV_Eng和Throttle文件读取数据

    返回:
    rpm_data: RPM数据数组
    throttle_data: 油门开度数据数组
    """
    try:
        # 读取RPM数据
        with open('Powertrain/AV_Eng', 'r') as f:
            rpm_data = np.array([float(line.strip()) for line in f.readlines()])

        # 读取Throttle数据
        with open('Powertrain/Throttle', 'r') as f:
            throttle_data = np.array([float(line.strip()) for line in f.readlines()])

        return rpm_data, throttle_data

    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        return None, None
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None, None

def calculate_torque_from_files():
    """
    从文件读取RPM和Throttle数据，计算对应的扭矩值

    返回:
    rpm_data: RPM数据
    throttle_data: 油门开度数据
    torque_data: 计算得到的扭矩数据
    """
    # 读取数据
    rpm_data, throttle_data = load_data_from_files()

    if rpm_data is None or throttle_data is None:
        return None, None, None

    print(f"成功读取数据:")
    print(f"  RPM数据点数: {len(rpm_data)}")
    print(f"  Throttle数据点数: {len(throttle_data)}")
    print(f"  RPM范围: {rpm_data.min():.2f} - {rpm_data.max():.2f}")
    print(f"  Throttle范围: {throttle_data.min():.4f} - {throttle_data.max():.4f}")

    # 计算每个数据点的扭矩
    torque_data = np.zeros(len(rpm_data))

    print("\n正在计算扭矩...")
    for i in range(len(rpm_data)):
        try:
            torque_data[i] = get_torque(rpm_data[i], throttle_data[i])
        except Exception as e:
            print(f"计算第{i+1}个数据点时出错: {e}")
            torque_data[i] = np.nan

        # 显示进度
        if (i + 1) % 1000 == 0:
            print(f"  已计算 {i+1}/{len(rpm_data)} 个数据点")

    print(f"扭矩计算完成!")
    print(f"  扭矩范围: {np.nanmin(torque_data):.2f} - {np.nanmax(torque_data):.2f} Nm")

    return rpm_data, throttle_data, torque_data

def plot_torque_data(rpm_data, throttle_data, torque_data):
    """
    绘制扭矩数据图表

    参数:
    rpm_data: RPM数据
    throttle_data: 油门开度数据
    torque_data: 扭矩数据
    """
    import matplotlib.pyplot as plt

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # 图1: 扭矩 vs 时间索引
    ax1.plot(range(len(torque_data)), torque_data, 'b-', linewidth=1)
    ax1.set_xlabel('数据点索引')
    ax1.set_ylabel('扭矩 [Nm]')
    ax1.set_title('扭矩随时间变化')
    ax1.grid(True, alpha=0.3)

    # 图2: 扭矩 vs RPM
    ax2.scatter(rpm_data, torque_data, c=throttle_data, cmap='viridis', s=1, alpha=0.6)
    ax2.set_xlabel('RPM')
    ax2.set_ylabel('扭矩 [Nm]')
    ax2.set_title('扭矩 vs RPM (颜色表示油门开度)')
    ax2.grid(True, alpha=0.3)
    cbar2 = plt.colorbar(ax2.collections[0], ax=ax2)
    cbar2.set_label('油门开度')

    # 图3: 扭矩 vs 油门开度
    ax3.scatter(throttle_data, torque_data, c=rpm_data, cmap='plasma', s=1, alpha=0.6)
    ax3.set_xlabel('油门开度')
    ax3.set_ylabel('扭矩 [Nm]')
    ax3.set_title('扭矩 vs 油门开度 (颜色表示RPM)')
    ax3.grid(True, alpha=0.3)
    cbar3 = plt.colorbar(ax3.collections[0], ax=ax3)
    cbar3.set_label('RPM')

    # 图4: RPM vs 油门开度 (颜色表示扭矩)
    scatter = ax4.scatter(throttle_data, rpm_data, c=torque_data, cmap='coolwarm', s=1, alpha=0.6)
    ax4.set_xlabel('油门开度')
    ax4.set_ylabel('RPM')
    ax4.set_title('RPM vs 油门开度 (颜色表示扭矩)')
    ax4.grid(True, alpha=0.3)
    cbar4 = plt.colorbar(scatter, ax=ax4)
    cbar4.set_label('扭矩 [Nm]')

    plt.tight_layout()

    # 保存图表
    plt.savefig('engine_torque_analysis.png', dpi=300, bbox_inches='tight')
    print(f"图表已保存为: engine_torque_analysis.png")

    return fig

# 主程序：从文件读取数据并绘制扭矩
if __name__ == "__main__":
    print(f"=== 发动机扭矩分析 ===")
    print(f"从AV_Eng和Throttle文件读取数据并计算扭矩")
    print("=" * 50)

    # 从文件计算扭矩数据
    rpm_data, throttle_data, torque_data = calculate_torque_from_files()

    if rpm_data is not None and throttle_data is not None and torque_data is not None:
        # 绘制图表
        print(f"\n正在生成图表...")
        plot_torque_data(rpm_data, throttle_data, torque_data)

        # 保存数据到CSV
        print(f"\n正在保存数据...")
        data_array = np.column_stack([rpm_data, throttle_data, torque_data])
        header = 'RPM,Throttle,Torque_Nm'
        np.savetxt('engine_torque_data.csv', data_array, delimiter=',', header=header, comments='')
        print(f"数据已保存为: engine_torque_data.csv")

        # 统计信息
        print(f"\n=== 统计信息 ===")
        print(f"数据点总数: {len(torque_data)}")
        print(f"有效数据点: {np.sum(~np.isnan(torque_data))}")
        print(f"RPM范围: {rpm_data.min():.2f} - {rpm_data.max():.2f}")
        print(f"油门开度范围: {throttle_data.min():.4f} - {throttle_data.max():.4f}")
        print(f"扭矩范围: {np.nanmin(torque_data):.2f} - {np.nanmax(torque_data):.2f} Nm")
        print(f"平均扭矩: {np.nanmean(torque_data):.2f} Nm")

    else:
        print(f"数据读取失败，无法进行分析")

    print(f"\n分析完成！")
