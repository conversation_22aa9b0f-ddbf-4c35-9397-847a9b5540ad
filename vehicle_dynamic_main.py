#!/usr/bin/env python3
"""
Vehicle Dynamics Main Module
============================

This module reads driving inputs and steering inputs for vehicle dynamics simulation.
It also loads vehicle simulation parameters from a YAML configuration file.

Inputs:
- Driving torque: MY_DR_L1(N-m), MY_DR_R1(N-m)
- Steering angles: Steer_L1(deg), Steer_R1(deg)
- Vehicle parameters: vehicle_simulation_config.yaml

Author: Generated for Vehicle Dynamics Simulation
Date: 2025-01-05
"""

import numpy as np
import math
import matplotlib.pyplot as plt
import os
import yaml
from typing import Dict, Any, Optional

# 导入MFPy轮胎模型库
import sys
sys.path.append('MF_Tire')
import mfpy

class VehicleDynamicsInputs:
    """
    Vehicle dynamics input data handler with YAML configuration support
    """

    def __init__(self, config_file="vehicle_simulation_config.yaml", dt=None):
        """
        Initialize the input data handler

        Parameters:
        config_file: str, path to YAML configuration file (default: "vehicle_simulation_config.yaml")
        dt: float, time step in seconds (overrides config file if provided)
        """
        # Load configuration from YAML file
        self.config = self.load_config(config_file)

        # Set time step (command line parameter overrides config file)
        if dt is not None:
            self.dt = dt
        else:
            self.dt = self.config.get('simulation', {}).get('dt', 0.001)

        # Initialize data storage
        self.time_steps = None
        self.time_array = None  # Actual time array in seconds
        self.MY_DR_L1 = None  # Left front wheel drive torque
        self.MY_DR_R1 = None  # Right front wheel drive torque
        self.Steer_L1 = None  # Left front wheel steering angle
        self.Steer_R1 = None  # Right front wheel steering angle
        self.data_loaded = False

        # Store configuration file path
        self.config_file = config_file

    def load_config(self, config_file: str) -> Dict[str, Any]:
        """
        Load configuration from YAML file

        Parameters:
        config_file: str, path to YAML configuration file

        Returns:
        dict: Configuration dictionary
        """
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"成功加载配置文件: {config_file}")
        return config

    def get_vehicle_parameters(self) -> Dict[str, Any]:
        """
        Get vehicle physical parameters from configuration

        Returns:
        dict: Vehicle parameters dictionary
        """
        vehicle_params = {}

        # Simulation parameters
        sim_config = self.config.get('simulation', {})
        vehicle_params.update({
            'dt': sim_config.get('dt', 0.001),
            'total_time': sim_config.get('total_time', 15.0),
            'frequency': sim_config.get('frequency', 1000)
        })

        # Vehicle basic parameters
        vehicle_config = self.config.get('vehicle', {})
        vehicle_params.update(vehicle_config)

        # Tire parameters
        tire_config = self.config.get('tire', {})
        vehicle_params.update(tire_config)

        # Environment parameters
        env_config = self.config.get('environment', {})
        vehicle_params.update(env_config)

        return vehicle_params

    def get_input_file_paths(self) -> Dict[str, str]:
        """
        Get input file paths from configuration

        Returns:
        dict: Input file paths dictionary
        """
        return self.config.get('input_files', {})

    def load_driving_inputs(self):
        """
        Load driving torque inputs from files specified in configuration

        Returns:
        bool: True if successful, False otherwise
        """
        try:
            # Get input file paths from configuration
            input_files = self.get_input_file_paths()

            # Load left front wheel drive torque
            left_torque_file = input_files.get('drive_torque_left', 'Powertrain/MY_DR_L1(N-m)')
            with open(left_torque_file, 'r') as f:
                self.MY_DR_L1 = np.array([float(line.strip()) for line in f.readlines()])

            # Load right front wheel drive torque
            right_torque_file = input_files.get('drive_torque_right', 'Powertrain/MY_DR_R1(N-m)')
            with open(right_torque_file, 'r') as f:
                self.MY_DR_R1 = np.array([float(line.strip()) for line in f.readlines()])

            print(f"成功读取驱动扭矩数据:")
            print(f"  MY_DR_L1 数据点数: {len(self.MY_DR_L1)}")
            print(f"  MY_DR_R1 数据点数: {len(self.MY_DR_R1)}")
            print(f"  MY_DR_L1 范围: {self.MY_DR_L1.min():.2f} - {self.MY_DR_L1.max():.2f} N·m")
            print(f"  MY_DR_R1 范围: {self.MY_DR_R1.min():.2f} - {self.MY_DR_R1.max():.2f} N·m")
            return True

        except FileNotFoundError as e:
            print(f"驱动扭矩文件未找到: {e}")
            return False
        except Exception as e:
            print(f"读取驱动扭矩文件时出错: {e}")
            return False

    def load_steering_inputs(self):
        """
        Load steering angle inputs from files specified in configuration

        Returns:
        bool: True if successful, False otherwise
        """
        try:
            # Get input file paths from configuration
            input_files = self.get_input_file_paths()

            # Load left front wheel steering angle
            left_steer_file = input_files.get('steering_angle_left', 'Wheel_Steer/Steer_L1(deg)')
            with open(left_steer_file, 'r') as f:
                self.Steer_L1 = np.array([float(line.strip()) for line in f.readlines()])

            # Load right front wheel steering angle
            right_steer_file = input_files.get('steering_angle_right', 'Wheel_Steer/Steer_R1(deg)')
            with open(right_steer_file, 'r') as f:
                self.Steer_R1 = np.array([float(line.strip()) for line in f.readlines()])

            print(f"成功读取转向角数据:")
            print(f"  Steer_L1 数据点数: {len(self.Steer_L1)}")
            print(f"  Steer_R1 数据点数: {len(self.Steer_R1)}")
            print(f"  Steer_L1 范围: {self.Steer_L1.min():.3f} - {self.Steer_L1.max():.3f} deg")
            print(f"  Steer_R1 范围: {self.Steer_R1.min():.3f} - {self.Steer_R1.max():.3f} deg")

            return True

        except FileNotFoundError as e:
            print(f"转向角文件未找到: {e}")
            return False
        except Exception as e:
            print(f"读取转向角文件时出错: {e}")
            return False

    def load_all_inputs(self):
        """
        Load all input data

        Returns:
        bool: True if all data loaded successfully, False otherwise
        """
        print("=== 加载车辆动力学输入数据 ===")

        # Load driving inputs
        drive_success = self.load_driving_inputs()

        # Load steering inputs
        steer_success = self.load_steering_inputs()

        if drive_success and steer_success:
            # Verify data consistency
            data_lengths = [
                len(self.MY_DR_L1),
                len(self.MY_DR_R1),
                len(self.Steer_L1),
                len(self.Steer_R1)
            ]

            if len(set(data_lengths)) == 1:
                self.time_steps = np.arange(len(self.MY_DR_L1))
                self.time_array = self.time_steps * self.dt  # Convert to actual time in seconds
                self.data_loaded = True
                total_time = (len(self.MY_DR_L1) - 1) * self.dt
                print(f"\n数据一致性检查通过:")
                print(f"  时间步数: {len(self.MY_DR_L1)}")
                print(f"  时间步长: {self.dt} 秒 ({self.dt*1000} 毫秒)")
                print(f"  总仿真时间: {total_time:.3f} 秒")
                return True
            else:
                print(f"\n数据长度不一致: {data_lengths}")
                return False
        else:
            print("\n数据加载失败")
            return False

    def get_inputs_at_time(self, time_index):
        """
        Get all inputs at a specific time index

        Parameters:
        time_index: int, time step index

        Returns:
        dict: Dictionary containing all inputs at the specified time
        """
        if not self.data_loaded:
            raise ValueError("数据未加载，请先调用 load_all_inputs()")

        if time_index < 0 or time_index >= len(self.time_steps):
            raise ValueError(f"时间索引 {time_index} 超出范围 [0, {len(self.time_steps)-1}]")

        return {
            'time_step': time_index,
            'time_sec': self.time_array[time_index],
            'dt': self.dt,
            'MY_DR_L1': self.MY_DR_L1[time_index],
            'MY_DR_R1': self.MY_DR_R1[time_index],
            'Steer_L1': self.Steer_L1[time_index],
            'Steer_R1': self.Steer_R1[time_index]
        }

    def get_simulation_info(self):
        """
        Get simulation timing information

        Returns:
        dict: Dictionary containing simulation parameters
        """
        if not self.data_loaded:
            raise ValueError("数据未加载，请先调用 load_all_inputs()")

        return {
            'dt': self.dt,
            'total_steps': len(self.time_steps),
            'total_time': self.time_array[-1],
            'frequency': 1.0 / self.dt,
            'time_array': self.time_array,
            'step_array': self.time_steps
        }

def main(config_file="vehicle_simulation_config.yaml"):
    """
    Main function for vehicle dynamics input processing

    Parameters:
    config_file: str, path to YAML configuration file
    """
    print("=== 车辆动力学输入数据处理 ===")
    print("读取驱动扭矩和转向角输入数据")
    print("加载车辆仿真参数配置")
    print("=" * 50)

    # Create input handler with configuration
    vehicle_inputs = VehicleDynamicsInputs(config_file)

    # Display configuration information
    print(f"\n=== 配置文件信息 ===")
    print(f"配置文件: {vehicle_inputs.config_file}")

    # Show key vehicle parameters
    vp = vehicle_inputs.get_vehicle_parameters()
    print(f"\n=== 车辆参数 ===")
    # print(f"车辆质量: {vp.get('mass')} kg")

    # 初始化车辆动力学变量
    Ax = 0.0  #纵向加速度，单位：m/s2
    Ay = 0.0  #侧向加速度，单位：m/s2
    AA_Y = 0.0 #车辆横摆角加速度，单位：rad/s2
    AAx = 0.0 #车辆簧载质量侧倾角加速度，单位：rad/s2
    AAy = 0.0 #车辆簧载质量俯仰角加速度，单位：rad/s2

    H_RCF = 0.0 #前非簧载质量质心到侧倾轴线的距离，单位：米
    H_RCR = 0.0 #后非簧载质量质心到侧倾轴线的距离，单位：米



    Fz_L1 = 0.0 #左前轮垂向力，单位：牛
    Fz_R1 = 0.0 #右前轮垂向力，单位：牛
    Fz_L2 = 0.0 #左后轮垂向力，单位：牛
    Fz_R2 = 0.0 #右后轮垂向力，单位：牛
    MY_DR_L1 = 0.0 #左前轮驱动扭矩，单位：牛米
    MY_DR_R1 = 0.0 #右前轮驱动扭矩，单位：牛米
    MY_DR_L2 = 0.0 #左后轮驱动扭矩，单位：牛米
    MY_DR_R2 = 0.0 #右后轮驱动扭矩，单位：牛米
    My_Bk_L1 = 0.0 #左前轮制动力矩，单位：牛米
    My_Bk_R1 = 0.0 #右前轮制动力矩，单位：牛米
    My_Bk_L2 = 0.0 #左后轮制动力矩，单位：牛米
    My_Bk_R2 = 0.0 #右后轮制动力矩，单位：牛米
    AAy_L1 = 0.0 #左前轮角加速度，单位：rad/s2
    AAy_R1 = 0.0 #右前轮角加速度，单位：rad/s2
    AAy_L2 = 0.0 #左后轮角加速度，单位：rad/s2
    AAy_R2 = 0.0 #右后轮角加速度，单位：rad/s2

    RRE_L1 = vp['radius'] #左前轮有效负载半径
    RRE_R1 = vp['radius'] #右前轮有效负载半径
    RRE_L2 = vp['radius'] #左后轮有效负载半径
    RRE_R2 = vp['radius'] #右后轮有效负载半径
    Fx_L1 = 0.0  #左前轮纵向力，单位：牛
    Fx_R1 = 0.0  #右前轮纵向力，单位：牛
    Fx_L2 = 0.0  #左后轮纵向力，单位：牛
    Fx_R2 = 0.0  #右后轮纵向力，单位：牛
    Fy_L1 = 0.0  #左前轮侧向力，单位：牛
    Fy_R1 = 0.0  #右前轮侧向力，单位：牛
    Fy_L2 = 0.0  #左后轮侧向力，单位：牛
    Fy_R2 = 0.0  #右后轮侧向力，单位：牛
    Vx_L1 = 0.0  #左前轮在车轮坐标系下的纵向速度，单位：m/s
    Vx_R1 = 0.0  #右前轮在车轮坐标系下的纵向速度，单位：m/s
    Vx_L2 = 0.0  #左后轮在车轮坐标系下的纵向速度，单位：m/s
    Vx_R2 = 0.0  #右后轮在车轮坐标系下的纵向速度，单位：m/s
    Vy_WC_L1 = 0.0  #左前轮在车轮坐标系下的侧向速度，单位：m/s
    Vy_WC_R1 = 0.0  #右前轮在车轮坐标系下的侧向速度，单位：m/s
    Vy_WC_L2 = 0.0  #左后轮在车轮坐标系下的侧向速度，单位：m/s
    Vy_WC_R2 = 0.0  #右后轮在车轮坐标系下的侧向速度，单位：m/s
    Alpha_L1 = 0.0 #左前轮侧偏角 rad
    Alpha_R1 = 0.0 #右前轮侧偏角 rad
    Alpha_L2 = 0.0 #左后轮侧偏角 rad
    Alpha_R2 = 0.0 #右后轮侧偏角 rad
    Gamma_L1 = 0.0 #左前轮外倾角 rad
    Gamma_R1 = 0.0 #右前轮外倾角 rad
    Gamma_L2 = 0.0 #左后轮外倾角 rad
    Gamma_R2 = 0.0 #右后轮外倾角 rad
    Mz_L1 = 0.0 #左前轮回正力矩，单位：牛米
    Mz_R1 = 0.0 #右前轮回正力矩，单位：牛米
    Mz_L2 = 0.0 #左后轮回正力矩，单位：牛米
    Mz_R2 = 0.0 #右后轮回正力矩，单位：牛米
    Fbz_L1 = 0.0 #左前悬架力，单位：牛
    Fbz_R1 = 0.0  #右前悬架力，单位：牛
    Fbz_L2 = 0.0  #左后悬架力，单位：牛
    Fbz_R2 = 0.0  #右后悬架力，单位：牛

    Vx = 33.33 #车辆纵向速度，单位：m/s
    Vy = 0.0 #车辆侧向速度，单位：m/s
    Vz = 0.0 #车辆垂向速度，单位：m/s
    Z = 0.0 # 簧载质量质心在垂直方向上的位移
    AVz = 0.0 #车辆横摆角速度，单位：rad/s
    Az = 0.0 #车辆垂向加速度，单位：m/s2
    Roll = 0.0 #车辆侧倾角，单位：rad
    AVx = 0.0 #车辆侧倾角速度，单位：rad/s
    Pitch = 0.0 #车辆俯仰角，单位：rad
    AVy = 0.0 #车辆俯仰角速度，单位：rad/s

    Fwind = 0.0 #风阻力
    CFx = 0.3 #纵向风阻系数
    D_AIR = 1.206 #空气密度
    AREA_AERO = 2.8 #迎风面积


    AVy_L1 = Vx / vp['radius'] #左前轮角速度，单位：rad/s
    AVy_R1 = Vx / vp['radius'] #右前轮角速度，单位：rad/s
    AVy_L2 = Vx / vp['radius'] #左后轮角速度，单位：rad/s
    AVy_R2 = Vx / vp['radius'] #右后轮角速度，单位：rad/s

    Kappa_L1 = 0.0 #左前轮纵向滑移率
    Kappa_R1 = 0.0 #右前轮纵向滑移率
    Kappa_L2 = 0.0 #左后轮纵向滑移率
    Kappa_R2 = 0.0 #右后轮纵向滑移率

    # 车辆动力学常量
    M_SU = vp['M_SU'] #簧上质量 (kg)
    Gravity = vp['Gravity'] #重力加速度 (m/s²)
    M_US = vp['M_US'] #前/后簧下质量都是用M_US

    # 将单位转换为国际单位
    LX_AXLE = vp['LX_AXLE'] / 1000 #轴距 (mm)
    LX_CG_SU = vp['LX_CG_SU'] / 1000 #重心到前轴距离 (mm)
    H_CG_SU = vp['H_CG_SU'] / 1000 #簧载质量质心离地高度 (mm)
    HWC_LF = vp['HWC_LF'] / 1000 #前非簧质量质心离地高度 (mm)
    HWC_LR = vp['HWC_LR'] / 1000 #后非簧质量质心离地高度 (mm)
    L_TRACK = vp['L_TRACK'] / 1000 #前后轮距都是用L_TRACK

    H_SU_R = H_CG_SU - vp['radius'] #簧载质量质心至侧倾中心的高度，单位：米
    H_SU_P = H_CG_SU - vp['radius']  # 簧载质量质心至俯仰中心的高度，单位：米



    # Load all input data
    if vehicle_inputs.load_all_inputs():
        print("\n=== 数据统计信息 ===")

        # Show simulation timing info
        sim_info = vehicle_inputs.get_simulation_info()
        print(f"\n=== 仿真时间信息 ===")
        print(f"时间步长: {sim_info['dt']} 秒 ({sim_info['dt']*1000} 毫秒)")
        print(f"采样频率: {sim_info['frequency']:.0f} Hz")
        print(f"总时间步数: {sim_info['total_steps']}")
        print(f"总仿真时间: {sim_info['total_time']:.3f} 秒")

        # 完整的车辆动力学仿真循环
        print(f"\n=== 开始车辆动力学仿真 ===")

        # 只在特定时间步输出详细结果
        output_steps = [0, 1000, 5000, 10000, len(vehicle_inputs.time_steps) - 1]

        # 遍历所有时间步进行完整计算
        for i in range(len(vehicle_inputs.time_steps)):
            inputs = vehicle_inputs.get_inputs_at_time(i)

            # 只在特定时间步输出基本信息
            if i in output_steps:
                print(f"时间步 {inputs['time_step']:5d} (t={inputs['time_sec']:6.3f}s): "
                      f"MY_DR_L1={inputs['MY_DR_L1']:8.2f}, MY_DR_R1={inputs['MY_DR_R1']:8.2f} N·m, "
                      f"Steer_L1={inputs['Steer_L1']:8.3f}, Steer_R1={inputs['Steer_R1']:8.3f} deg")

                # 对于前轮转角的简化
                Steer_L1 = math.radians(inputs['Steer_L1'])
                Steer_R1 = math.radians(inputs['Steer_R1'])

                # 各轮角加速度计算
                # 左前轮角加速度
                AAy_L1 = (inputs['MY_DR_L1'] - My_Bk_L1 - Fx_L1 * RRE_L1) / vp['IW_L']
                # 左前轮角速度
                AVy_L1 = AVy_L1 + AAy_L1 * vp['dt']
            # # 调试信息（只在前几步和关键时间点输出）
            # if i <= 10 or i in output_steps:
            #     print(f"  步骤{i:4d}: 扭矩={inputs['MY_DR_L1']:8.2f}N·m, 轮胎力={Fx_L1:8.2f}N, 角加速度={AAy_L1:8.2f}rad/s², Δω={delta_omega:8.6f}rad/s, 累积ω={AVy_L1:8.6f}rad/s")
                # 右前轮角加速度
                AAy_R1 = (inputs['MY_DR_R1'] - My_Bk_R1 - Fx_R1 * RRE_R1) / vp['IW_R']
                # 右前轮角速度
                AVy_R1 = AVy_R1 + AAy_R1 * vp['dt']
                # 左后轮角加速度
                AAy_L2 = (MY_DR_L2 - My_Bk_L2 - Fx_L2 * RRE_L2) / vp['IW_L']
                # 左后轮角速度
                AVy_L2 = AVy_L2 + AAy_L2 * vp['dt']
                # 右后轮角加速度
                AAy_R2 = (MY_DR_R2 - My_Bk_R2 - Fx_R2 * RRE_R2) / vp['IW_R']
                # 右后轮角速度
                AVy_R2 = AVy_R2 + AAy_R2 * vp['dt']


                # 各车轮轮心在车轮坐标系下的速度
                # 左前轮在车轮坐标系下的纵向速度
                Vx_L1 = (Vx - L_TRACK * AVz / 2) * math.cos(Steer_L1) + (Vy + LX_CG_SU * AVz) * math.sin(Steer_L1)
                # 右前轮在车轮坐标系下的纵向速度
                Vx_R1 = (Vx + L_TRACK * AVz / 2) * math.cos(Steer_R1) + (Vy + LX_CG_SU * AVz) * math.sin(Steer_R1)
                # 左后轮在车轮坐标系下的纵向速度
                Vx_L2 = Vx - L_TRACK * AVz / 2
                # 右后轮在车轮坐标系下的纵向速度
                Vx_R2 = Vx + L_TRACK * AVz / 2
                # 左前轮在车轮坐标系下的侧向速度
                Vy_WC_L1 = -(Vx - L_TRACK * AVz / 2) * math.sin(Steer_L1) + (Vy + LX_CG_SU * AVz) * math.cos(Steer_L1)
                # 右前轮在车轮坐标系下的侧向速度
                Vy_WC_R1 = -(Vx + L_TRACK * AVz / 2) * math.sin(Steer_R1) + (Vy + LX_CG_SU * AVz) * math.cos(Steer_R1)
                # 左后轮在车轮坐标系下的纵向速度
                Vy_WC_L2 = Vy - (LX_AXLE - LX_CG_SU) * AVz
                # 右后轮在车轮坐标系下的纵向速度
                Vy_WC_R2 = Vy - (LX_AXLE - LX_CG_SU) * AVz

                # 计算各车轮的纵向滑移率
                # 左前轮的纵向滑移率
                if Vx_L1 < 0.556:
                    Vx_L1 = 0.556
                Kappa_L1 = (AVy_L1 * RRE_L1 - Vx_L1) / Vx_L1
                # 右前轮的纵向滑移率
                if Vx_R1 < 0.556:
                    Vx_R1 = 0.556
                Kappa_R1 = (AVy_R1 * RRE_R1 - Vx_R1) / Vx_R1
                # 左后轮的纵向滑移率
                if Vx_L2 < 0.556:
                    Vx_L2 = 0.556
                Kappa_L2 = (AVy_L2 * RRE_L2 - Vx_L2) / Vx_L2
                # 右后轮的纵向滑移率
                if Vx_R2 < 0.556:
                    Vx_R2 = 0.556
                Kappa_R2 = (AVy_R2 * RRE_R2 - Vx_R2) / Vx_R2

                # 计算各车轮的侧偏角
                # 添加小的epsilon值避免除零错误
                epsilon = 0.001
                vx_safe = max(abs(Vx), epsilon)

                # 左前轮的侧偏角
                Alpha_L1 = math.atan((Vy + LX_CG_SU * AVz) / (vx_safe - L_TRACK * AVz / 2)) - Steer_L1
                # 右前轮的侧偏角
                Alpha_R1 = math.atan((Vy + LX_CG_SU * AVz) / (vx_safe + L_TRACK * AVz / 2)) - Steer_R1
                # 左后轮的侧偏角
                Alpha_L2 = math.atan((Vy - (LX_AXLE - LX_CG_SU) * AVz) / (vx_safe - L_TRACK * AVz / 2))
                # 右后轮的侧偏角
                Alpha_R2 = math.atan((Vy - (LX_AXLE - LX_CG_SU) * AVz) / (vx_safe + L_TRACK * AVz / 2))

                # 各轮胎垂直载荷，以前左轮为例
                # 第一项表示簧载质量产生的静载荷：M_SU * Gravity * (LX_AXLE - LX_CG_SU) / (2 * LX_AXLE)
                # 第二项表示非簧载质量：M_US * Gravity / 2
                # 第三项表示由纵向加速度产生的载荷变化： Ax * (M_SU * H_CG_SU + M_US * HWC_LF + M_US * HWC_LR) / (2 * LX_AXLE)
                # 第四项表示由侧向加速度产生的载荷变化：Ay * (M_SU * H_RCF * (LX_AXLE - LX_CG_SU) / LX_AXLE + M_US * HWC_LF) / L_TRACK
                # 左前轮垂直载荷公式：
                Fz_L1 = M_SU * Gravity * (LX_AXLE - LX_CG_SU) / (2 * LX_AXLE) + M_US * Gravity / 2 - (Ax * (M_SU * H_CG_SU + M_US * HWC_LF + M_US * HWC_LR) / (2 * LX_AXLE)) - (Ay * (M_SU * H_RCF * (LX_AXLE - LX_CG_SU) / LX_AXLE + M_US * HWC_LF) / L_TRACK)
                # 右前轮垂直载荷公式：
                Fz_R1 = M_SU * Gravity * (LX_AXLE - LX_CG_SU) / (2 * LX_AXLE) + M_US * Gravity / 2 - (Ax * (M_SU * H_CG_SU + M_US * HWC_LF + M_US * HWC_LR) / (2 * LX_AXLE)) + (Ay * (M_SU * H_RCF * (LX_AXLE - LX_CG_SU) / LX_AXLE + M_US * HWC_LF) / L_TRACK)
                # 左后轮垂直载荷公式：
                Fz_L2 = M_SU * Gravity * LX_CG_SU / (2 * LX_AXLE) + M_US * Gravity / 2 + (Ax * (M_SU * H_CG_SU + M_US * HWC_LF + M_US * HWC_LR) / (2 * LX_AXLE)) - (Ay * (M_SU * H_RCR * LX_CG_SU / LX_AXLE + M_US * HWC_LR) / L_TRACK)
                # 右后轮垂直载荷公式：
                Fz_R2 = M_SU * Gravity * LX_CG_SU / (2 * LX_AXLE) + M_US * Gravity / 2 + (Ax * (M_SU * H_CG_SU + M_US * HWC_LF + M_US * HWC_LR) / (2 * LX_AXLE)) + (Ay * (M_SU * H_RCR * LX_CG_SU / LX_AXLE + M_US * HWC_LR) / L_TRACK)

                # 计算各轮的轮胎力和力矩
                # 加载轮胎参数文件（只在第一次时加载）
                if i == 0:
                    try:
                        tir_file_path = 'MF_Tire/samples/pacejka_tir52_carsim.tir'
                        tir_data = mfpy.preprocessing.read_tir(tir_file_path)
                        print(f"成功加载轮胎参数文件: {tir_file_path}")
                        tire_model_available = True
                    except Exception as e:
                        print(f"加载轮胎参数文件失败: {e}")
                        tire_model_available = False

                # 计算各轮胎力和力矩（使用Pacejka轮胎模型）
                if tire_model_available:

                    # 左前轮轮胎力计算
                    try:
                        # 输入参数：[侧偏角, 滑移率, 外倾角, 垂直载荷, 纵向速度] (需要是数组格式)
                        alpha_L1 = np.array([Alpha_L1])
                        kappa_L1 = np.array([Kappa_L1])
                        gamma_L1 = np.array([Gamma_L1])
                        fz_L1 = np.array([Fz_L1])
                        vx_L1 = np.array([max(abs(Vx_L1), 0.556)])  # 确保速度不低于VXLOW

                        input_L1 = [alpha_L1, kappa_L1, gamma_L1, fz_L1, vx_L1]
                        result_L1 = mfpy.solve(input_L1, tir_data, check_limits=False)

                        Fx_L1 = result_L1.FX[0]  # 纵向力
                        Fy_L1 = result_L1.FY[0]  # 侧向力
                        Mx_L1 = result_L1.MX[0]  # 翻转力矩
                        My_L1 = result_L1.MY[0]  # 滚动阻力矩
                        Mz_L1 = result_L1.MZ[0]  # 回正力矩
                    except Exception as e:
                        print(f"左前轮轮胎力计算失败: {e}")
                        Fx_L1 = Fy_L1 = Mx_L1 = My_L1 = Mz_L1 = 0.0

                    # 右前轮轮胎力计算
                    try:
                        alpha_R1 = np.array([Alpha_R1])
                        kappa_R1 = np.array([Kappa_R1])
                        gamma_R1 = np.array([Gamma_R1])
                        fz_R1 = np.array([Fz_R1])
                        vx_R1 = np.array([max(abs(Vx_R1), 0.556)])  # 确保速度不低于VXLOW

                        input_R1 = [alpha_R1, kappa_R1, gamma_R1, fz_R1, vx_R1]
                        result_R1 = mfpy.solve(input_R1, tir_data, check_limits=False)

                        Fx_R1 = result_R1.FX[0]  # 纵向力
                        Fy_R1 = result_R1.FY[0]  # 侧向力
                        Mx_R1 = result_R1.MX[0]  # 翻转力矩
                        My_R1 = result_R1.MY[0]  # 滚动阻力矩
                        Mz_R1 = result_R1.MZ[0]  # 回正力矩
                    except Exception as e:
                        print(f"右前轮轮胎力计算失败: {e}")
                        Fx_R1 = Fy_R1 = Mx_R1 = My_R1 = Mz_R1 = 0.0

                    # 左后轮轮胎力计算
                    try:
                        alpha_L2 = np.array([Alpha_L2])
                        kappa_L2 = np.array([Kappa_L2])
                        gamma_L2 = np.array([Gamma_L2])
                        fz_L2 = np.array([Fz_L2])
                        vx_L2 = np.array([max(abs(Vx_L2), 0.556)])  # 确保速度不低于VXLOW

                        input_L2 = [alpha_L2, kappa_L2, gamma_L2, fz_L2, vx_L2]
                        result_L2 = mfpy.solve(input_L2, tir_data, check_limits=False)

                        Fx_L2 = result_L2.FX[0]  # 纵向力
                        Fy_L2 = result_L2.FY[0]  # 侧向力
                        Mx_L2 = result_L2.MX[0]  # 翻转力矩
                        My_L2 = result_L2.MY[0]  # 滚动阻力矩
                        Mz_L2 = result_L2.MZ[0]  # 回正力矩
                    except Exception as e:
                        print(f"左后轮轮胎力计算失败: {e}")
                        Fx_L2 = Fy_L2 = Mx_L2 = My_L2 = Mz_L2 = 0.0

                    # 右后轮轮胎力计算
                    try:
                        alpha_R2 = np.array([Alpha_R2])
                        kappa_R2 = np.array([Kappa_R2])
                        gamma_R2 = np.array([Gamma_R2])
                        fz_R2 = np.array([Fz_R2])
                        vx_R2 = np.array([max(abs(Vx_R2), 0.556)])  # 确保速度不低于VXLOW

                        input_R2 = [alpha_R2, kappa_R2, gamma_R2, fz_R2, vx_R2]
                        result_R2 = mfpy.solve(input_R2, tir_data, check_limits=False)

                        Fx_R2 = result_R2.FX[0]  # 纵向力
                        Fy_R2 = result_R2.FY[0]  # 侧向力
                        Mx_R2 = result_R2.MX[0]  # 翻转力矩
                        My_R2 = result_R2.MY[0]  # 滚动阻力矩
                        Mz_R2 = result_R2.MZ[0]  # 回正力矩
                    except Exception as e:
                        print(f"右后轮轮胎力计算失败: {e}")
                        Fx_R2 = Fy_R2 = Mx_R2 = My_R2 = Mz_R2 = 0.0


                # 整车动力学纵向微分方程
                # X方向的风阻
                Fwind = 0.5 * CFx * AREA_AERO * D_AIR * Vx * Vx
                Ax = AVz * Vy + ((Fx_L1 * math.cos(Steer_L1)) + (Fx_R1 * math.cos(Steer_R1)) - (Fy_L1 * math.sin(Steer_L1)) - (Fy_R1 * math.sin(Steer_R1)) + Fx_L2 + Fx_R2 - Fwind) / (M_SU + 2 * M_US)
                # 更新车辆纵向速度Vx
                Vx = Vx + Ax * vp['dt']
                # 整车动力学侧向微分方程
                Ay = -AVz * Vx + ((Fx_L1 * math.sin(Steer_L1)) + (Fx_R1 * math.sin(Steer_R1)) + (Fy_L1 * math.cos(Steer_L1)) + (Fy_R1 * math.cos(Steer_R1)) + Fy_L2 + Fy_R2) / (M_SU + 2 * M_US)
                # 更新车辆纵向速度Vx
                Vy = Vy + Ay * vp['dt']
                # 整车动力学横摆微分方程
                AA_Y = (((Fx_L1 * math.sin(Steer_L1)) + (Fx_R1 * math.sin(Steer_R1)) + (Fy_L1 * math.cos(Steer_L1)) + (Fy_R1 * math.cos(Steer_R1))) * LX_CG_SU
                        - (Fy_L2 + Fy_R2) * (LX_AXLE - LX_CG_SU) - (Fx_L1 * math.cos(Steer_L1) - Fx_R1 * math.cos(Steer_R1) - Fy_L1 * math.sin(Steer_L1) + Fy_R1 * math.sin(Steer_R1)) * 0.5 * L_TRACK
                        - (Fx_L2 - Fx_R2) * 0.5 * L_TRACK + Mz_L1 + Mz_R1 + Mz_L2 + Mz_R2) / vp['IZZ_SU']
                # 更新车辆横摆角速度AVz
                AVz = AVz + AA_Y * vp['dt']

                #悬架系列方程



                # 整车垂向动力学微分方程
                Az = ((Fbz_L1 + Fbz_R1 + Fbz_L2 + Fbz_R2 - M_SU * Gravity * math.cos(Pitch) * math.cos(Roll)) / M_SU) + Vx * AVy - Vy * AVx
                # 更新车辆垂向加速度Az
                Vz = Vz + Az * vp['dt']
                # 更新簧载质量质心在垂直方向上的位移
                Z = Z + Vz * vp['dt']
                # 整车侧倾动力学微分方程
                AAx = ((Fbz_L2 - Fbz_R2) * 0.5 * L_TRACK + (Fbz_L1 - Fbz_R1) * 0.5 * L_TRACK + M_SU * (Ay + Vx * AVz) * H_SU_R
                     + M_SU * Gravity * H_SU_R * math.sin(Roll) + (vp['IYY_SU'] - vp['IZZ_SU']) * AVy * AVz) / vp['IXX_SU']
                # 更新车辆侧倾角速度AVx和侧倾角
                AVx = AVx + AAx * vp['dt']
                Roll = Roll + AVx * vp['dt']
                # 整车俯仰动力学微分方程
                AAy = ((Fbz_L2 + Fbz_R2) * (LX_AXLE - LX_CG_SU) - (Fbz_L1 + Fbz_R1) * LX_CG_SU + M_SU * (Ax - Vy * AVz) * H_SU_P
                       + M_SU * Gravity * H_SU_P * math.sin(Pitch) + (vp['IZZ_SU'] - vp['IXX_SU']) * AVx * AVz) / vp['IYY_SU']
                # 更新车辆俯仰角速度AVy和俯仰角
                AVy = AAy + AAy * vp['dt']
                Pitch = Pitch + AVy * vp['dt']










        return vehicle_inputs
    else:
        print("数据加载失败，请检查文件路径和格式")
        return None

if __name__ == "__main__":
    # Run main function with default configuration file
    vehicle_inputs = main("vehicle_simulation_config.yaml")